# 覃蹇之家 - 情侣专属网站

## 🏠 项目简介

这是一个专为情侣打造的私人网站，提供博客记录、任务管理、照片分享、纪念日管理等功能。网站采用现代化的技术栈，界面温馨浪漫，功能实用贴心。

## ✨ 主要特性

- 💕 **恋爱时刻表**：实时显示在一起的时间，温馨浪漫（参考poetize.cn/love设计）
- 📝 **博客系统**：Notion风格块编辑器，支持富文本、代码、图片等多种内容类型
- ✅ **任务管理**：拖拽式卡片任务，支持公开/私密设置
- 📸 **相册管理**：自定义标签分类，批量上传处理，标签云展示
- 🎨 **主题切换**：4套精美主题（浪漫粉、清新绿、优雅蓝、暖色橙）
- 🔒 **权限控制**：双账号系统，访客只读模式
- 💌 **飞车传信**：情侣间私密消息传递，实时通信
- 🎯 **祝福板**：朋友祝福留言功能，支持审核机制

## 🛠️ 技术栈

### 前端
- Vue 3 + Composition API
- Vite 构建工具
- Element Plus UI组件库
- Pinia 状态管理
- Vue Router 4 路由管理
- Axios HTTP客户端
- @tiptap/vue-3 富文本编辑器（Notion风格）
- Vue.Draggable.Next 拖拽功能
- @vueuse/motion 动画库

### 后端
- FastAPI (Python 3.9+)
- SQLAlchemy 2.0 ORM
- MySQL 8.0 数据库
- MinIO 对象存储（本地部署）
- JWT 身份验证
- Pillow 图片处理

### 部署
- Docker + Docker Compose
- Nginx 反向代理

## 📁 项目结构

```
love-website/
├── frontend/                 # Vue 3 前端项目
│   ├── src/
│   │   ├── components/      # 公共组件
│   │   ├── views/          # 页面组件
│   │   ├── stores/         # Pinia状态管理
│   │   ├── utils/          # 工具函数
│   │   └── styles/         # 样式文件
│   ├── public/
│   └── package.json
├── backend/                  # FastAPI 后端项目
│   ├── app/
│   │   ├── models/         # 数据模型
│   │   ├── routers/        # API路由
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── main.py         # 应用入口
│   ├── requirements.txt
│   └── Dockerfile
├── docker-compose.yml        # Docker编排文件
├── init.sql                 # 数据库初始化脚本
├── nginx.conf               # Nginx配置
└── README.md
```

## 🚀 快速开始

### 环境要求
- Node.js 16+
- Python 3.9+
- Docker & Docker Compose
- MySQL 8.0

### 1. 克隆项目
```bash
git clone <repository-url>
cd love-website
```

### 2. 启动基础服务
```bash
# 启动MySQL和MinIO
docker-compose up mysql minio -d
```

### 3. 后端设置
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install fastapi uvicorn sqlalchemy pymysql python-multipart pillow minio python-jose[cryptography] passlib[bcrypt] websockets

# 初始化数据库
mysql -u user -p love_website < ../init.sql

# 启动后端服务
uvicorn main:app --reload
```

### 4. 前端设置
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 5. 访问应用
- 前端应用：http://localhost:5173
- 后端API：http://localhost:8000
- API文档：http://localhost:8000/docs
- MinIO控制台：http://localhost:9001

## 🔧 配置说明

### 环境变量配置

创建 `backend/.env` 文件：
```env
# 数据库配置
DATABASE_URL=mysql://user:password@localhost:3306/love_website

# JWT配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# MinIO配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=love-website

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp
```

### 默认账户

系统预置了两个账户：
- **主账户**：username: `admin`, password: `admin123`
- **女朋友账户**：username: `girlfriend`, password: `girlfriend123`

⚠️ **重要**：首次部署后请立即修改默认密码！

## 📋 功能说明

### 1. 首页（参考poetize.cn/love设计）
- 顶部双人头像和爱心图标
- 恋爱时刻表实时计时显示
- 飞车传信快捷入口
- 功能模块卡片导航（点点滴滴、时光相册、祝福板）
- 标签云和相册展示区域

### 2. 博客模块（Notion风格）
- Notion风格块编辑器
- 支持多种内容类型：文本、标题、代码、图片、表格、列表等
- 拖拽排序功能
- 文章分类和标签
- 草稿自动保存功能
- 公开/私密权限控制
- 实时协作编辑

### 3. 任务管理
- 拖拽式卡片布局
- 多种排列方式（网格、圆形、水平、垂直、对角线）
- 优先级和进度管理
- 截止时间提醒
- 公开/私密设置

### 4. 相册管理
- 相册创建和管理
- 批量图片上传
- 自动生成缩略图
- 自定义标签系统和标签云展示
- 幻灯片浏览模式
- 照片标签筛选功能

### 5. 纪念日管理
- 重要日期记录
- 恋爱时长实时计算（参考poetize.cn计时器）
- 提醒功能和倒计时
- 历史记录查看

### 6. 飞车传信
- 情侣间私密消息发送
- 实时消息接收通知
- 消息历史记录查看
- 支持文本、图片、文件消息

### 7. 祝福板
- 朋友祝福留言提交
- 祝福内容展示
- 管理员审核机制
- IP地址记录防刷

### 8. 主题系统
- 浪漫粉色主题
- 清新绿色主题
- 优雅蓝色主题
- 暖色橙色主题

## 🔒 安全特性

- JWT身份验证
- 密码bcrypt加密
- SQL注入防护
- XSS攻击防护
- 文件上传安全检查
- 权限分级控制

## 📱 响应式设计

网站完全支持移动端访问，在手机、平板等设备上都有良好的用户体验。

## 🎯 开发计划

项目开发分为6个阶段，预计18-22天完成：

1. **项目初始化** (1-2天)
2. **用户认证系统** (2-3天)
3. **核心功能模块开发** (10-12天)
   - 博客模块（Notion风格）(3-4天)
   - 任务管理模块 (2-3天)
   - 相册模块（标签云）(2-3天)
   - 纪念日模块 (1-2天)
   - 飞车传信模块 (1-2天)
   - 祝福板模块 (1天)
4. **UI优化和主题系统** (3-4天)
5. **测试和部署** (2-3天)
6. **文档和维护** (1天)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 💕 致谢

感谢所有为这个项目贡献代码和想法的朋友们！

---

**愿这个小小的网站，记录下你们最美好的时光！** 💕
