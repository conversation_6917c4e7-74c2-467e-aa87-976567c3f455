{"version": 3, "sources": ["../../prosemirror-tables/dist/index.js", "../../@tiptap/extension-table/src/utilities/colStyle.ts", "../../@tiptap/extension-table/src/TableView.ts", "../../@tiptap/extension-table/src/utilities/createColGroup.ts", "../../@tiptap/extension-table/src/utilities/createCell.ts", "../../@tiptap/extension-table/src/utilities/getTableNodeTypes.ts", "../../@tiptap/extension-table/src/utilities/createTable.ts", "../../@tiptap/extension-table/src/utilities/isCellSelection.ts", "../../@tiptap/extension-table/src/utilities/deleteTableWhenAllCellsSelected.ts", "../../@tiptap/extension-table/src/table.ts"], "sourcesContent": ["// src/index.ts\nimport { Plugin as Plugin2 } from \"prosemirror-state\";\n\n// src/cellselection.ts\nimport { Fragment, Slice } from \"prosemirror-model\";\nimport {\n  NodeSelection as NodeSelection2,\n  Selection,\n  SelectionRange,\n  TextSelection\n} from \"prosemirror-state\";\nimport { Decoration, DecorationSet } from \"prosemirror-view\";\n\n// src/tablemap.ts\nvar readFromCache;\nvar addToCache;\nif (typeof WeakMap != \"undefined\") {\n  let cache = /* @__PURE__ */ new WeakMap();\n  readFromCache = (key) => cache.get(key);\n  addToCache = (key, value) => {\n    cache.set(key, value);\n    return value;\n  };\n} else {\n  const cache = [];\n  const cacheSize = 10;\n  let cachePos = 0;\n  readFromCache = (key) => {\n    for (let i = 0; i < cache.length; i += 2)\n      if (cache[i] == key) return cache[i + 1];\n  };\n  addToCache = (key, value) => {\n    if (cachePos == cacheSize) cachePos = 0;\n    cache[cachePos++] = key;\n    return cache[cachePos++] = value;\n  };\n}\nvar TableMap = class {\n  constructor(width, height, map, problems) {\n    this.width = width;\n    this.height = height;\n    this.map = map;\n    this.problems = problems;\n  }\n  // Find the dimensions of the cell at the given position.\n  findCell(pos) {\n    for (let i = 0; i < this.map.length; i++) {\n      const curPos = this.map[i];\n      if (curPos != pos) continue;\n      const left = i % this.width;\n      const top = i / this.width | 0;\n      let right = left + 1;\n      let bottom = top + 1;\n      for (let j = 1; right < this.width && this.map[i + j] == curPos; j++) {\n        right++;\n      }\n      for (let j = 1; bottom < this.height && this.map[i + this.width * j] == curPos; j++) {\n        bottom++;\n      }\n      return { left, top, right, bottom };\n    }\n    throw new RangeError(`No cell with offset ${pos} found`);\n  }\n  // Find the left side of the cell at the given position.\n  colCount(pos) {\n    for (let i = 0; i < this.map.length; i++) {\n      if (this.map[i] == pos) {\n        return i % this.width;\n      }\n    }\n    throw new RangeError(`No cell with offset ${pos} found`);\n  }\n  // Find the next cell in the given direction, starting from the cell\n  // at `pos`, if any.\n  nextCell(pos, axis, dir) {\n    const { left, right, top, bottom } = this.findCell(pos);\n    if (axis == \"horiz\") {\n      if (dir < 0 ? left == 0 : right == this.width) return null;\n      return this.map[top * this.width + (dir < 0 ? left - 1 : right)];\n    } else {\n      if (dir < 0 ? top == 0 : bottom == this.height) return null;\n      return this.map[left + this.width * (dir < 0 ? top - 1 : bottom)];\n    }\n  }\n  // Get the rectangle spanning the two given cells.\n  rectBetween(a, b) {\n    const {\n      left: leftA,\n      right: rightA,\n      top: topA,\n      bottom: bottomA\n    } = this.findCell(a);\n    const {\n      left: leftB,\n      right: rightB,\n      top: topB,\n      bottom: bottomB\n    } = this.findCell(b);\n    return {\n      left: Math.min(leftA, leftB),\n      top: Math.min(topA, topB),\n      right: Math.max(rightA, rightB),\n      bottom: Math.max(bottomA, bottomB)\n    };\n  }\n  // Return the position of all cells that have the top left corner in\n  // the given rectangle.\n  cellsInRect(rect) {\n    const result = [];\n    const seen = {};\n    for (let row = rect.top; row < rect.bottom; row++) {\n      for (let col = rect.left; col < rect.right; col++) {\n        const index = row * this.width + col;\n        const pos = this.map[index];\n        if (seen[pos]) continue;\n        seen[pos] = true;\n        if (col == rect.left && col && this.map[index - 1] == pos || row == rect.top && row && this.map[index - this.width] == pos) {\n          continue;\n        }\n        result.push(pos);\n      }\n    }\n    return result;\n  }\n  // Return the position at which the cell at the given row and column\n  // starts, or would start, if a cell started there.\n  positionAt(row, col, table) {\n    for (let i = 0, rowStart = 0; ; i++) {\n      const rowEnd = rowStart + table.child(i).nodeSize;\n      if (i == row) {\n        let index = col + row * this.width;\n        const rowEndIndex = (row + 1) * this.width;\n        while (index < rowEndIndex && this.map[index] < rowStart) index++;\n        return index == rowEndIndex ? rowEnd - 1 : this.map[index];\n      }\n      rowStart = rowEnd;\n    }\n  }\n  // Find the table map for the given table node.\n  static get(table) {\n    return readFromCache(table) || addToCache(table, computeMap(table));\n  }\n};\nfunction computeMap(table) {\n  if (table.type.spec.tableRole != \"table\")\n    throw new RangeError(\"Not a table node: \" + table.type.name);\n  const width = findWidth(table), height = table.childCount;\n  const map = [];\n  let mapPos = 0;\n  let problems = null;\n  const colWidths = [];\n  for (let i = 0, e = width * height; i < e; i++) map[i] = 0;\n  for (let row = 0, pos = 0; row < height; row++) {\n    const rowNode = table.child(row);\n    pos++;\n    for (let i = 0; ; i++) {\n      while (mapPos < map.length && map[mapPos] != 0) mapPos++;\n      if (i == rowNode.childCount) break;\n      const cellNode = rowNode.child(i);\n      const { colspan, rowspan, colwidth } = cellNode.attrs;\n      for (let h = 0; h < rowspan; h++) {\n        if (h + row >= height) {\n          (problems || (problems = [])).push({\n            type: \"overlong_rowspan\",\n            pos,\n            n: rowspan - h\n          });\n          break;\n        }\n        const start = mapPos + h * width;\n        for (let w = 0; w < colspan; w++) {\n          if (map[start + w] == 0) map[start + w] = pos;\n          else\n            (problems || (problems = [])).push({\n              type: \"collision\",\n              row,\n              pos,\n              n: colspan - w\n            });\n          const colW = colwidth && colwidth[w];\n          if (colW) {\n            const widthIndex = (start + w) % width * 2, prev = colWidths[widthIndex];\n            if (prev == null || prev != colW && colWidths[widthIndex + 1] == 1) {\n              colWidths[widthIndex] = colW;\n              colWidths[widthIndex + 1] = 1;\n            } else if (prev == colW) {\n              colWidths[widthIndex + 1]++;\n            }\n          }\n        }\n      }\n      mapPos += colspan;\n      pos += cellNode.nodeSize;\n    }\n    const expectedPos = (row + 1) * width;\n    let missing = 0;\n    while (mapPos < expectedPos) if (map[mapPos++] == 0) missing++;\n    if (missing)\n      (problems || (problems = [])).push({ type: \"missing\", row, n: missing });\n    pos++;\n  }\n  if (width === 0 || height === 0)\n    (problems || (problems = [])).push({ type: \"zero_sized\" });\n  const tableMap = new TableMap(width, height, map, problems);\n  let badWidths = false;\n  for (let i = 0; !badWidths && i < colWidths.length; i += 2)\n    if (colWidths[i] != null && colWidths[i + 1] < height) badWidths = true;\n  if (badWidths) findBadColWidths(tableMap, colWidths, table);\n  return tableMap;\n}\nfunction findWidth(table) {\n  let width = -1;\n  let hasRowSpan = false;\n  for (let row = 0; row < table.childCount; row++) {\n    const rowNode = table.child(row);\n    let rowWidth = 0;\n    if (hasRowSpan)\n      for (let j = 0; j < row; j++) {\n        const prevRow = table.child(j);\n        for (let i = 0; i < prevRow.childCount; i++) {\n          const cell = prevRow.child(i);\n          if (j + cell.attrs.rowspan > row) rowWidth += cell.attrs.colspan;\n        }\n      }\n    for (let i = 0; i < rowNode.childCount; i++) {\n      const cell = rowNode.child(i);\n      rowWidth += cell.attrs.colspan;\n      if (cell.attrs.rowspan > 1) hasRowSpan = true;\n    }\n    if (width == -1) width = rowWidth;\n    else if (width != rowWidth) width = Math.max(width, rowWidth);\n  }\n  return width;\n}\nfunction findBadColWidths(map, colWidths, table) {\n  if (!map.problems) map.problems = [];\n  const seen = {};\n  for (let i = 0; i < map.map.length; i++) {\n    const pos = map.map[i];\n    if (seen[pos]) continue;\n    seen[pos] = true;\n    const node = table.nodeAt(pos);\n    if (!node) {\n      throw new RangeError(`No cell with offset ${pos} found`);\n    }\n    let updated = null;\n    const attrs = node.attrs;\n    for (let j = 0; j < attrs.colspan; j++) {\n      const col = (i + j) % map.width;\n      const colWidth = colWidths[col * 2];\n      if (colWidth != null && (!attrs.colwidth || attrs.colwidth[j] != colWidth))\n        (updated || (updated = freshColWidth(attrs)))[j] = colWidth;\n    }\n    if (updated)\n      map.problems.unshift({\n        type: \"colwidth mismatch\",\n        pos,\n        colwidth: updated\n      });\n  }\n}\nfunction freshColWidth(attrs) {\n  if (attrs.colwidth) return attrs.colwidth.slice();\n  const result = [];\n  for (let i = 0; i < attrs.colspan; i++) result.push(0);\n  return result;\n}\n\n// src/util.ts\nimport { PluginKey } from \"prosemirror-state\";\n\n// src/schema.ts\nfunction getCellAttrs(dom, extraAttrs) {\n  if (typeof dom === \"string\") {\n    return {};\n  }\n  const widthAttr = dom.getAttribute(\"data-colwidth\");\n  const widths = widthAttr && /^\\d+(,\\d+)*$/.test(widthAttr) ? widthAttr.split(\",\").map((s) => Number(s)) : null;\n  const colspan = Number(dom.getAttribute(\"colspan\") || 1);\n  const result = {\n    colspan,\n    rowspan: Number(dom.getAttribute(\"rowspan\") || 1),\n    colwidth: widths && widths.length == colspan ? widths : null\n  };\n  for (const prop in extraAttrs) {\n    const getter = extraAttrs[prop].getFromDOM;\n    const value = getter && getter(dom);\n    if (value != null) {\n      result[prop] = value;\n    }\n  }\n  return result;\n}\nfunction setCellAttrs(node, extraAttrs) {\n  const attrs = {};\n  if (node.attrs.colspan != 1) attrs.colspan = node.attrs.colspan;\n  if (node.attrs.rowspan != 1) attrs.rowspan = node.attrs.rowspan;\n  if (node.attrs.colwidth)\n    attrs[\"data-colwidth\"] = node.attrs.colwidth.join(\",\");\n  for (const prop in extraAttrs) {\n    const setter = extraAttrs[prop].setDOMAttr;\n    if (setter) setter(node.attrs[prop], attrs);\n  }\n  return attrs;\n}\nfunction validateColwidth(value) {\n  if (value === null) {\n    return;\n  }\n  if (!Array.isArray(value)) {\n    throw new TypeError(\"colwidth must be null or an array\");\n  }\n  for (const item of value) {\n    if (typeof item !== \"number\") {\n      throw new TypeError(\"colwidth must be null or an array of numbers\");\n    }\n  }\n}\nfunction tableNodes(options) {\n  const extraAttrs = options.cellAttributes || {};\n  const cellAttrs = {\n    colspan: { default: 1, validate: \"number\" },\n    rowspan: { default: 1, validate: \"number\" },\n    colwidth: { default: null, validate: validateColwidth }\n  };\n  for (const prop in extraAttrs)\n    cellAttrs[prop] = {\n      default: extraAttrs[prop].default,\n      validate: extraAttrs[prop].validate\n    };\n  return {\n    table: {\n      content: \"table_row+\",\n      tableRole: \"table\",\n      isolating: true,\n      group: options.tableGroup,\n      parseDOM: [{ tag: \"table\" }],\n      toDOM() {\n        return [\"table\", [\"tbody\", 0]];\n      }\n    },\n    table_row: {\n      content: \"(table_cell | table_header)*\",\n      tableRole: \"row\",\n      parseDOM: [{ tag: \"tr\" }],\n      toDOM() {\n        return [\"tr\", 0];\n      }\n    },\n    table_cell: {\n      content: options.cellContent,\n      attrs: cellAttrs,\n      tableRole: \"cell\",\n      isolating: true,\n      parseDOM: [\n        { tag: \"td\", getAttrs: (dom) => getCellAttrs(dom, extraAttrs) }\n      ],\n      toDOM(node) {\n        return [\"td\", setCellAttrs(node, extraAttrs), 0];\n      }\n    },\n    table_header: {\n      content: options.cellContent,\n      attrs: cellAttrs,\n      tableRole: \"header_cell\",\n      isolating: true,\n      parseDOM: [\n        { tag: \"th\", getAttrs: (dom) => getCellAttrs(dom, extraAttrs) }\n      ],\n      toDOM(node) {\n        return [\"th\", setCellAttrs(node, extraAttrs), 0];\n      }\n    }\n  };\n}\nfunction tableNodeTypes(schema) {\n  let result = schema.cached.tableNodeTypes;\n  if (!result) {\n    result = schema.cached.tableNodeTypes = {};\n    for (const name in schema.nodes) {\n      const type = schema.nodes[name], role = type.spec.tableRole;\n      if (role) result[role] = type;\n    }\n  }\n  return result;\n}\n\n// src/util.ts\nvar tableEditingKey = new PluginKey(\"selectingCells\");\nfunction cellAround($pos) {\n  for (let d = $pos.depth - 1; d > 0; d--)\n    if ($pos.node(d).type.spec.tableRole == \"row\")\n      return $pos.node(0).resolve($pos.before(d + 1));\n  return null;\n}\nfunction cellWrapping($pos) {\n  for (let d = $pos.depth; d > 0; d--) {\n    const role = $pos.node(d).type.spec.tableRole;\n    if (role === \"cell\" || role === \"header_cell\") return $pos.node(d);\n  }\n  return null;\n}\nfunction isInTable(state) {\n  const $head = state.selection.$head;\n  for (let d = $head.depth; d > 0; d--)\n    if ($head.node(d).type.spec.tableRole == \"row\") return true;\n  return false;\n}\nfunction selectionCell(state) {\n  const sel = state.selection;\n  if (\"$anchorCell\" in sel && sel.$anchorCell) {\n    return sel.$anchorCell.pos > sel.$headCell.pos ? sel.$anchorCell : sel.$headCell;\n  } else if (\"node\" in sel && sel.node && sel.node.type.spec.tableRole == \"cell\") {\n    return sel.$anchor;\n  }\n  const $cell = cellAround(sel.$head) || cellNear(sel.$head);\n  if ($cell) {\n    return $cell;\n  }\n  throw new RangeError(`No cell found around position ${sel.head}`);\n}\nfunction cellNear($pos) {\n  for (let after = $pos.nodeAfter, pos = $pos.pos; after; after = after.firstChild, pos++) {\n    const role = after.type.spec.tableRole;\n    if (role == \"cell\" || role == \"header_cell\") return $pos.doc.resolve(pos);\n  }\n  for (let before = $pos.nodeBefore, pos = $pos.pos; before; before = before.lastChild, pos--) {\n    const role = before.type.spec.tableRole;\n    if (role == \"cell\" || role == \"header_cell\")\n      return $pos.doc.resolve(pos - before.nodeSize);\n  }\n}\nfunction pointsAtCell($pos) {\n  return $pos.parent.type.spec.tableRole == \"row\" && !!$pos.nodeAfter;\n}\nfunction moveCellForward($pos) {\n  return $pos.node(0).resolve($pos.pos + $pos.nodeAfter.nodeSize);\n}\nfunction inSameTable($cellA, $cellB) {\n  return $cellA.depth == $cellB.depth && $cellA.pos >= $cellB.start(-1) && $cellA.pos <= $cellB.end(-1);\n}\nfunction findCell($pos) {\n  return TableMap.get($pos.node(-1)).findCell($pos.pos - $pos.start(-1));\n}\nfunction colCount($pos) {\n  return TableMap.get($pos.node(-1)).colCount($pos.pos - $pos.start(-1));\n}\nfunction nextCell($pos, axis, dir) {\n  const table = $pos.node(-1);\n  const map = TableMap.get(table);\n  const tableStart = $pos.start(-1);\n  const moved = map.nextCell($pos.pos - tableStart, axis, dir);\n  return moved == null ? null : $pos.node(0).resolve(tableStart + moved);\n}\nfunction removeColSpan(attrs, pos, n = 1) {\n  const result = { ...attrs, colspan: attrs.colspan - n };\n  if (result.colwidth) {\n    result.colwidth = result.colwidth.slice();\n    result.colwidth.splice(pos, n);\n    if (!result.colwidth.some((w) => w > 0)) result.colwidth = null;\n  }\n  return result;\n}\nfunction addColSpan(attrs, pos, n = 1) {\n  const result = { ...attrs, colspan: attrs.colspan + n };\n  if (result.colwidth) {\n    result.colwidth = result.colwidth.slice();\n    for (let i = 0; i < n; i++) result.colwidth.splice(pos, 0, 0);\n  }\n  return result;\n}\nfunction columnIsHeader(map, table, col) {\n  const headerCell = tableNodeTypes(table.type.schema).header_cell;\n  for (let row = 0; row < map.height; row++)\n    if (table.nodeAt(map.map[col + row * map.width]).type != headerCell)\n      return false;\n  return true;\n}\n\n// src/cellselection.ts\nvar CellSelection = class _CellSelection extends Selection {\n  // A table selection is identified by its anchor and head cells. The\n  // positions given to this constructor should point _before_ two\n  // cells in the same table. They may be the same, to select a single\n  // cell.\n  constructor($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const rect = map.rectBetween(\n      $anchorCell.pos - tableStart,\n      $headCell.pos - tableStart\n    );\n    const doc = $anchorCell.node(0);\n    const cells = map.cellsInRect(rect).filter((p) => p != $headCell.pos - tableStart);\n    cells.unshift($headCell.pos - tableStart);\n    const ranges = cells.map((pos) => {\n      const cell = table.nodeAt(pos);\n      if (!cell) {\n        throw RangeError(`No cell with offset ${pos} found`);\n      }\n      const from = tableStart + pos + 1;\n      return new SelectionRange(\n        doc.resolve(from),\n        doc.resolve(from + cell.content.size)\n      );\n    });\n    super(ranges[0].$from, ranges[0].$to, ranges);\n    this.$anchorCell = $anchorCell;\n    this.$headCell = $headCell;\n  }\n  map(doc, mapping) {\n    const $anchorCell = doc.resolve(mapping.map(this.$anchorCell.pos));\n    const $headCell = doc.resolve(mapping.map(this.$headCell.pos));\n    if (pointsAtCell($anchorCell) && pointsAtCell($headCell) && inSameTable($anchorCell, $headCell)) {\n      const tableChanged = this.$anchorCell.node(-1) != $anchorCell.node(-1);\n      if (tableChanged && this.isRowSelection())\n        return _CellSelection.rowSelection($anchorCell, $headCell);\n      else if (tableChanged && this.isColSelection())\n        return _CellSelection.colSelection($anchorCell, $headCell);\n      else return new _CellSelection($anchorCell, $headCell);\n    }\n    return TextSelection.between($anchorCell, $headCell);\n  }\n  // Returns a rectangular slice of table rows containing the selected\n  // cells.\n  content() {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const rect = map.rectBetween(\n      this.$anchorCell.pos - tableStart,\n      this.$headCell.pos - tableStart\n    );\n    const seen = {};\n    const rows = [];\n    for (let row = rect.top; row < rect.bottom; row++) {\n      const rowContent = [];\n      for (let index = row * map.width + rect.left, col = rect.left; col < rect.right; col++, index++) {\n        const pos = map.map[index];\n        if (seen[pos]) continue;\n        seen[pos] = true;\n        const cellRect = map.findCell(pos);\n        let cell = table.nodeAt(pos);\n        if (!cell) {\n          throw RangeError(`No cell with offset ${pos} found`);\n        }\n        const extraLeft = rect.left - cellRect.left;\n        const extraRight = cellRect.right - rect.right;\n        if (extraLeft > 0 || extraRight > 0) {\n          let attrs = cell.attrs;\n          if (extraLeft > 0) {\n            attrs = removeColSpan(attrs, 0, extraLeft);\n          }\n          if (extraRight > 0) {\n            attrs = removeColSpan(\n              attrs,\n              attrs.colspan - extraRight,\n              extraRight\n            );\n          }\n          if (cellRect.left < rect.left) {\n            cell = cell.type.createAndFill(attrs);\n            if (!cell) {\n              throw RangeError(\n                `Could not create cell with attrs ${JSON.stringify(attrs)}`\n              );\n            }\n          } else {\n            cell = cell.type.create(attrs, cell.content);\n          }\n        }\n        if (cellRect.top < rect.top || cellRect.bottom > rect.bottom) {\n          const attrs = {\n            ...cell.attrs,\n            rowspan: Math.min(cellRect.bottom, rect.bottom) - Math.max(cellRect.top, rect.top)\n          };\n          if (cellRect.top < rect.top) {\n            cell = cell.type.createAndFill(attrs);\n          } else {\n            cell = cell.type.create(attrs, cell.content);\n          }\n        }\n        rowContent.push(cell);\n      }\n      rows.push(table.child(row).copy(Fragment.from(rowContent)));\n    }\n    const fragment = this.isColSelection() && this.isRowSelection() ? table : rows;\n    return new Slice(Fragment.from(fragment), 1, 1);\n  }\n  replace(tr, content = Slice.empty) {\n    const mapFrom = tr.steps.length, ranges = this.ranges;\n    for (let i = 0; i < ranges.length; i++) {\n      const { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n      tr.replace(\n        mapping.map($from.pos),\n        mapping.map($to.pos),\n        i ? Slice.empty : content\n      );\n    }\n    const sel = Selection.findFrom(\n      tr.doc.resolve(tr.mapping.slice(mapFrom).map(this.to)),\n      -1\n    );\n    if (sel) tr.setSelection(sel);\n  }\n  replaceWith(tr, node) {\n    this.replace(tr, new Slice(Fragment.from(node), 0, 0));\n  }\n  forEachCell(f) {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const cells = map.cellsInRect(\n      map.rectBetween(\n        this.$anchorCell.pos - tableStart,\n        this.$headCell.pos - tableStart\n      )\n    );\n    for (let i = 0; i < cells.length; i++) {\n      f(table.nodeAt(cells[i]), tableStart + cells[i]);\n    }\n  }\n  // True if this selection goes all the way from the top to the\n  // bottom of the table.\n  isColSelection() {\n    const anchorTop = this.$anchorCell.index(-1);\n    const headTop = this.$headCell.index(-1);\n    if (Math.min(anchorTop, headTop) > 0) return false;\n    const anchorBottom = anchorTop + this.$anchorCell.nodeAfter.attrs.rowspan;\n    const headBottom = headTop + this.$headCell.nodeAfter.attrs.rowspan;\n    return Math.max(anchorBottom, headBottom) == this.$headCell.node(-1).childCount;\n  }\n  // Returns the smallest column selection that covers the given anchor\n  // and head cell.\n  static colSelection($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const anchorRect = map.findCell($anchorCell.pos - tableStart);\n    const headRect = map.findCell($headCell.pos - tableStart);\n    const doc = $anchorCell.node(0);\n    if (anchorRect.top <= headRect.top) {\n      if (anchorRect.top > 0)\n        $anchorCell = doc.resolve(tableStart + map.map[anchorRect.left]);\n      if (headRect.bottom < map.height)\n        $headCell = doc.resolve(\n          tableStart + map.map[map.width * (map.height - 1) + headRect.right - 1]\n        );\n    } else {\n      if (headRect.top > 0)\n        $headCell = doc.resolve(tableStart + map.map[headRect.left]);\n      if (anchorRect.bottom < map.height)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[map.width * (map.height - 1) + anchorRect.right - 1]\n        );\n    }\n    return new _CellSelection($anchorCell, $headCell);\n  }\n  // True if this selection goes all the way from the left to the\n  // right of the table.\n  isRowSelection() {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const anchorLeft = map.colCount(this.$anchorCell.pos - tableStart);\n    const headLeft = map.colCount(this.$headCell.pos - tableStart);\n    if (Math.min(anchorLeft, headLeft) > 0) return false;\n    const anchorRight = anchorLeft + this.$anchorCell.nodeAfter.attrs.colspan;\n    const headRight = headLeft + this.$headCell.nodeAfter.attrs.colspan;\n    return Math.max(anchorRight, headRight) == map.width;\n  }\n  eq(other) {\n    return other instanceof _CellSelection && other.$anchorCell.pos == this.$anchorCell.pos && other.$headCell.pos == this.$headCell.pos;\n  }\n  // Returns the smallest row selection that covers the given anchor\n  // and head cell.\n  static rowSelection($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const anchorRect = map.findCell($anchorCell.pos - tableStart);\n    const headRect = map.findCell($headCell.pos - tableStart);\n    const doc = $anchorCell.node(0);\n    if (anchorRect.left <= headRect.left) {\n      if (anchorRect.left > 0)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[anchorRect.top * map.width]\n        );\n      if (headRect.right < map.width)\n        $headCell = doc.resolve(\n          tableStart + map.map[map.width * (headRect.top + 1) - 1]\n        );\n    } else {\n      if (headRect.left > 0)\n        $headCell = doc.resolve(tableStart + map.map[headRect.top * map.width]);\n      if (anchorRect.right < map.width)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[map.width * (anchorRect.top + 1) - 1]\n        );\n    }\n    return new _CellSelection($anchorCell, $headCell);\n  }\n  toJSON() {\n    return {\n      type: \"cell\",\n      anchor: this.$anchorCell.pos,\n      head: this.$headCell.pos\n    };\n  }\n  static fromJSON(doc, json) {\n    return new _CellSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n  }\n  static create(doc, anchorCell, headCell = anchorCell) {\n    return new _CellSelection(doc.resolve(anchorCell), doc.resolve(headCell));\n  }\n  getBookmark() {\n    return new CellBookmark(this.$anchorCell.pos, this.$headCell.pos);\n  }\n};\nCellSelection.prototype.visible = false;\nSelection.jsonID(\"cell\", CellSelection);\nvar CellBookmark = class _CellBookmark {\n  constructor(anchor, head) {\n    this.anchor = anchor;\n    this.head = head;\n  }\n  map(mapping) {\n    return new _CellBookmark(mapping.map(this.anchor), mapping.map(this.head));\n  }\n  resolve(doc) {\n    const $anchorCell = doc.resolve(this.anchor), $headCell = doc.resolve(this.head);\n    if ($anchorCell.parent.type.spec.tableRole == \"row\" && $headCell.parent.type.spec.tableRole == \"row\" && $anchorCell.index() < $anchorCell.parent.childCount && $headCell.index() < $headCell.parent.childCount && inSameTable($anchorCell, $headCell))\n      return new CellSelection($anchorCell, $headCell);\n    else return Selection.near($headCell, 1);\n  }\n};\nfunction drawCellSelection(state) {\n  if (!(state.selection instanceof CellSelection)) return null;\n  const cells = [];\n  state.selection.forEachCell((node, pos) => {\n    cells.push(\n      Decoration.node(pos, pos + node.nodeSize, { class: \"selectedCell\" })\n    );\n  });\n  return DecorationSet.create(state.doc, cells);\n}\nfunction isCellBoundarySelection({ $from, $to }) {\n  if ($from.pos == $to.pos || $from.pos < $to.pos - 6) return false;\n  let afterFrom = $from.pos;\n  let beforeTo = $to.pos;\n  let depth = $from.depth;\n  for (; depth >= 0; depth--, afterFrom++)\n    if ($from.after(depth + 1) < $from.end(depth)) break;\n  for (let d = $to.depth; d >= 0; d--, beforeTo--)\n    if ($to.before(d + 1) > $to.start(d)) break;\n  return afterFrom == beforeTo && /row|table/.test($from.node(depth).type.spec.tableRole);\n}\nfunction isTextSelectionAcrossCells({ $from, $to }) {\n  let fromCellBoundaryNode;\n  let toCellBoundaryNode;\n  for (let i = $from.depth; i > 0; i--) {\n    const node = $from.node(i);\n    if (node.type.spec.tableRole === \"cell\" || node.type.spec.tableRole === \"header_cell\") {\n      fromCellBoundaryNode = node;\n      break;\n    }\n  }\n  for (let i = $to.depth; i > 0; i--) {\n    const node = $to.node(i);\n    if (node.type.spec.tableRole === \"cell\" || node.type.spec.tableRole === \"header_cell\") {\n      toCellBoundaryNode = node;\n      break;\n    }\n  }\n  return fromCellBoundaryNode !== toCellBoundaryNode && $to.parentOffset === 0;\n}\nfunction normalizeSelection(state, tr, allowTableNodeSelection) {\n  const sel = (tr || state).selection;\n  const doc = (tr || state).doc;\n  let normalize;\n  let role;\n  if (sel instanceof NodeSelection2 && (role = sel.node.type.spec.tableRole)) {\n    if (role == \"cell\" || role == \"header_cell\") {\n      normalize = CellSelection.create(doc, sel.from);\n    } else if (role == \"row\") {\n      const $cell = doc.resolve(sel.from + 1);\n      normalize = CellSelection.rowSelection($cell, $cell);\n    } else if (!allowTableNodeSelection) {\n      const map = TableMap.get(sel.node);\n      const start = sel.from + 1;\n      const lastCell = start + map.map[map.width * map.height - 1];\n      normalize = CellSelection.create(doc, start + 1, lastCell);\n    }\n  } else if (sel instanceof TextSelection && isCellBoundarySelection(sel)) {\n    normalize = TextSelection.create(doc, sel.from);\n  } else if (sel instanceof TextSelection && isTextSelectionAcrossCells(sel)) {\n    normalize = TextSelection.create(doc, sel.$from.start(), sel.$from.end());\n  }\n  if (normalize) (tr || (tr = state.tr)).setSelection(normalize);\n  return tr;\n}\n\n// src/fixtables.ts\nimport { PluginKey as PluginKey2 } from \"prosemirror-state\";\nvar fixTablesKey = new PluginKey2(\"fix-tables\");\nfunction changedDescendants(old, cur, offset, f) {\n  const oldSize = old.childCount, curSize = cur.childCount;\n  outer: for (let i = 0, j = 0; i < curSize; i++) {\n    const child = cur.child(i);\n    for (let scan = j, e = Math.min(oldSize, i + 3); scan < e; scan++) {\n      if (old.child(scan) == child) {\n        j = scan + 1;\n        offset += child.nodeSize;\n        continue outer;\n      }\n    }\n    f(child, offset);\n    if (j < oldSize && old.child(j).sameMarkup(child))\n      changedDescendants(old.child(j), child, offset + 1, f);\n    else child.nodesBetween(0, child.content.size, f, offset + 1);\n    offset += child.nodeSize;\n  }\n}\nfunction fixTables(state, oldState) {\n  let tr;\n  const check = (node, pos) => {\n    if (node.type.spec.tableRole == \"table\")\n      tr = fixTable(state, node, pos, tr);\n  };\n  if (!oldState) state.doc.descendants(check);\n  else if (oldState.doc != state.doc)\n    changedDescendants(oldState.doc, state.doc, 0, check);\n  return tr;\n}\nfunction fixTable(state, table, tablePos, tr) {\n  const map = TableMap.get(table);\n  if (!map.problems) return tr;\n  if (!tr) tr = state.tr;\n  const mustAdd = [];\n  for (let i = 0; i < map.height; i++) mustAdd.push(0);\n  for (let i = 0; i < map.problems.length; i++) {\n    const prob = map.problems[i];\n    if (prob.type == \"collision\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell) continue;\n      const attrs = cell.attrs;\n      for (let j = 0; j < attrs.rowspan; j++) mustAdd[prob.row + j] += prob.n;\n      tr.setNodeMarkup(\n        tr.mapping.map(tablePos + 1 + prob.pos),\n        null,\n        removeColSpan(attrs, attrs.colspan - prob.n, prob.n)\n      );\n    } else if (prob.type == \"missing\") {\n      mustAdd[prob.row] += prob.n;\n    } else if (prob.type == \"overlong_rowspan\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell) continue;\n      tr.setNodeMarkup(tr.mapping.map(tablePos + 1 + prob.pos), null, {\n        ...cell.attrs,\n        rowspan: cell.attrs.rowspan - prob.n\n      });\n    } else if (prob.type == \"colwidth mismatch\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell) continue;\n      tr.setNodeMarkup(tr.mapping.map(tablePos + 1 + prob.pos), null, {\n        ...cell.attrs,\n        colwidth: prob.colwidth\n      });\n    } else if (prob.type == \"zero_sized\") {\n      const pos = tr.mapping.map(tablePos);\n      tr.delete(pos, pos + table.nodeSize);\n    }\n  }\n  let first, last;\n  for (let i = 0; i < mustAdd.length; i++)\n    if (mustAdd[i]) {\n      if (first == null) first = i;\n      last = i;\n    }\n  for (let i = 0, pos = tablePos + 1; i < map.height; i++) {\n    const row = table.child(i);\n    const end = pos + row.nodeSize;\n    const add = mustAdd[i];\n    if (add > 0) {\n      let role = \"cell\";\n      if (row.firstChild) {\n        role = row.firstChild.type.spec.tableRole;\n      }\n      const nodes = [];\n      for (let j = 0; j < add; j++) {\n        const node = tableNodeTypes(state.schema)[role].createAndFill();\n        if (node) nodes.push(node);\n      }\n      const side = (i == 0 || first == i - 1) && last == i ? pos + 1 : end - 1;\n      tr.insert(tr.mapping.map(side), nodes);\n    }\n    pos = end;\n  }\n  return tr.setMeta(fixTablesKey, { fixTables: true });\n}\n\n// src/input.ts\nimport { keydownHandler } from \"prosemirror-keymap\";\nimport { Fragment as Fragment4 } from \"prosemirror-model\";\nimport {\n  Selection as Selection2,\n  TextSelection as TextSelection3\n} from \"prosemirror-state\";\n\n// src/commands.ts\nimport {\n  Fragment as Fragment2,\n  Slice as Slice2\n} from \"prosemirror-model\";\nimport {\n  TextSelection as TextSelection2\n} from \"prosemirror-state\";\nfunction selectedRect(state) {\n  const sel = state.selection;\n  const $pos = selectionCell(state);\n  const table = $pos.node(-1);\n  const tableStart = $pos.start(-1);\n  const map = TableMap.get(table);\n  const rect = sel instanceof CellSelection ? map.rectBetween(\n    sel.$anchorCell.pos - tableStart,\n    sel.$headCell.pos - tableStart\n  ) : map.findCell($pos.pos - tableStart);\n  return { ...rect, tableStart, map, table };\n}\nfunction addColumn(tr, { map, tableStart, table }, col) {\n  let refColumn = col > 0 ? -1 : 0;\n  if (columnIsHeader(map, table, col + refColumn)) {\n    refColumn = col == 0 || col == map.width ? null : 0;\n  }\n  for (let row = 0; row < map.height; row++) {\n    const index = row * map.width + col;\n    if (col > 0 && col < map.width && map.map[index - 1] == map.map[index]) {\n      const pos = map.map[index];\n      const cell = table.nodeAt(pos);\n      tr.setNodeMarkup(\n        tr.mapping.map(tableStart + pos),\n        null,\n        addColSpan(cell.attrs, col - map.colCount(pos))\n      );\n      row += cell.attrs.rowspan - 1;\n    } else {\n      const type = refColumn == null ? tableNodeTypes(table.type.schema).cell : table.nodeAt(map.map[index + refColumn]).type;\n      const pos = map.positionAt(row, col, table);\n      tr.insert(tr.mapping.map(tableStart + pos), type.createAndFill());\n    }\n  }\n  return tr;\n}\nfunction addColumnBefore(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addColumn(state.tr, rect, rect.left));\n  }\n  return true;\n}\nfunction addColumnAfter(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addColumn(state.tr, rect, rect.right));\n  }\n  return true;\n}\nfunction removeColumn(tr, { map, table, tableStart }, col) {\n  const mapStart = tr.mapping.maps.length;\n  for (let row = 0; row < map.height; ) {\n    const index = row * map.width + col;\n    const pos = map.map[index];\n    const cell = table.nodeAt(pos);\n    const attrs = cell.attrs;\n    if (col > 0 && map.map[index - 1] == pos || col < map.width - 1 && map.map[index + 1] == pos) {\n      tr.setNodeMarkup(\n        tr.mapping.slice(mapStart).map(tableStart + pos),\n        null,\n        removeColSpan(attrs, col - map.colCount(pos))\n      );\n    } else {\n      const start = tr.mapping.slice(mapStart).map(tableStart + pos);\n      tr.delete(start, start + cell.nodeSize);\n    }\n    row += attrs.rowspan;\n  }\n}\nfunction deleteColumn(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    const tr = state.tr;\n    if (rect.left == 0 && rect.right == rect.map.width) return false;\n    for (let i = rect.right - 1; ; i--) {\n      removeColumn(tr, rect, i);\n      if (i == rect.left) break;\n      const table = rect.tableStart ? tr.doc.nodeAt(rect.tableStart - 1) : tr.doc;\n      if (!table) {\n        throw RangeError(\"No table found\");\n      }\n      rect.table = table;\n      rect.map = TableMap.get(table);\n    }\n    dispatch(tr);\n  }\n  return true;\n}\nfunction rowIsHeader(map, table, row) {\n  var _a;\n  const headerCell = tableNodeTypes(table.type.schema).header_cell;\n  for (let col = 0; col < map.width; col++)\n    if (((_a = table.nodeAt(map.map[col + row * map.width])) == null ? void 0 : _a.type) != headerCell)\n      return false;\n  return true;\n}\nfunction addRow(tr, { map, tableStart, table }, row) {\n  var _a;\n  let rowPos = tableStart;\n  for (let i = 0; i < row; i++) rowPos += table.child(i).nodeSize;\n  const cells = [];\n  let refRow = row > 0 ? -1 : 0;\n  if (rowIsHeader(map, table, row + refRow))\n    refRow = row == 0 || row == map.height ? null : 0;\n  for (let col = 0, index = map.width * row; col < map.width; col++, index++) {\n    if (row > 0 && row < map.height && map.map[index] == map.map[index - map.width]) {\n      const pos = map.map[index];\n      const attrs = table.nodeAt(pos).attrs;\n      tr.setNodeMarkup(tableStart + pos, null, {\n        ...attrs,\n        rowspan: attrs.rowspan + 1\n      });\n      col += attrs.colspan - 1;\n    } else {\n      const type = refRow == null ? tableNodeTypes(table.type.schema).cell : (_a = table.nodeAt(map.map[index + refRow * map.width])) == null ? void 0 : _a.type;\n      const node = type == null ? void 0 : type.createAndFill();\n      if (node) cells.push(node);\n    }\n  }\n  tr.insert(rowPos, tableNodeTypes(table.type.schema).row.create(null, cells));\n  return tr;\n}\nfunction addRowBefore(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addRow(state.tr, rect, rect.top));\n  }\n  return true;\n}\nfunction addRowAfter(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addRow(state.tr, rect, rect.bottom));\n  }\n  return true;\n}\nfunction removeRow(tr, { map, table, tableStart }, row) {\n  let rowPos = 0;\n  for (let i = 0; i < row; i++) rowPos += table.child(i).nodeSize;\n  const nextRow = rowPos + table.child(row).nodeSize;\n  const mapFrom = tr.mapping.maps.length;\n  tr.delete(rowPos + tableStart, nextRow + tableStart);\n  const seen = /* @__PURE__ */ new Set();\n  for (let col = 0, index = row * map.width; col < map.width; col++, index++) {\n    const pos = map.map[index];\n    if (seen.has(pos)) continue;\n    seen.add(pos);\n    if (row > 0 && pos == map.map[index - map.width]) {\n      const attrs = table.nodeAt(pos).attrs;\n      tr.setNodeMarkup(tr.mapping.slice(mapFrom).map(pos + tableStart), null, {\n        ...attrs,\n        rowspan: attrs.rowspan - 1\n      });\n      col += attrs.colspan - 1;\n    } else if (row < map.height && pos == map.map[index + map.width]) {\n      const cell = table.nodeAt(pos);\n      const attrs = cell.attrs;\n      const copy = cell.type.create(\n        { ...attrs, rowspan: cell.attrs.rowspan - 1 },\n        cell.content\n      );\n      const newPos = map.positionAt(row + 1, col, table);\n      tr.insert(tr.mapping.slice(mapFrom).map(tableStart + newPos), copy);\n      col += attrs.colspan - 1;\n    }\n  }\n}\nfunction deleteRow(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state), tr = state.tr;\n    if (rect.top == 0 && rect.bottom == rect.map.height) return false;\n    for (let i = rect.bottom - 1; ; i--) {\n      removeRow(tr, rect, i);\n      if (i == rect.top) break;\n      const table = rect.tableStart ? tr.doc.nodeAt(rect.tableStart - 1) : tr.doc;\n      if (!table) {\n        throw RangeError(\"No table found\");\n      }\n      rect.table = table;\n      rect.map = TableMap.get(rect.table);\n    }\n    dispatch(tr);\n  }\n  return true;\n}\nfunction isEmpty(cell) {\n  const c = cell.content;\n  return c.childCount == 1 && c.child(0).isTextblock && c.child(0).childCount == 0;\n}\nfunction cellsOverlapRectangle({ width, height, map }, rect) {\n  let indexTop = rect.top * width + rect.left, indexLeft = indexTop;\n  let indexBottom = (rect.bottom - 1) * width + rect.left, indexRight = indexTop + (rect.right - rect.left - 1);\n  for (let i = rect.top; i < rect.bottom; i++) {\n    if (rect.left > 0 && map[indexLeft] == map[indexLeft - 1] || rect.right < width && map[indexRight] == map[indexRight + 1])\n      return true;\n    indexLeft += width;\n    indexRight += width;\n  }\n  for (let i = rect.left; i < rect.right; i++) {\n    if (rect.top > 0 && map[indexTop] == map[indexTop - width] || rect.bottom < height && map[indexBottom] == map[indexBottom + width])\n      return true;\n    indexTop++;\n    indexBottom++;\n  }\n  return false;\n}\nfunction mergeCells(state, dispatch) {\n  const sel = state.selection;\n  if (!(sel instanceof CellSelection) || sel.$anchorCell.pos == sel.$headCell.pos)\n    return false;\n  const rect = selectedRect(state), { map } = rect;\n  if (cellsOverlapRectangle(map, rect)) return false;\n  if (dispatch) {\n    const tr = state.tr;\n    const seen = {};\n    let content = Fragment2.empty;\n    let mergedPos;\n    let mergedCell;\n    for (let row = rect.top; row < rect.bottom; row++) {\n      for (let col = rect.left; col < rect.right; col++) {\n        const cellPos = map.map[row * map.width + col];\n        const cell = rect.table.nodeAt(cellPos);\n        if (seen[cellPos] || !cell) continue;\n        seen[cellPos] = true;\n        if (mergedPos == null) {\n          mergedPos = cellPos;\n          mergedCell = cell;\n        } else {\n          if (!isEmpty(cell)) content = content.append(cell.content);\n          const mapped = tr.mapping.map(cellPos + rect.tableStart);\n          tr.delete(mapped, mapped + cell.nodeSize);\n        }\n      }\n    }\n    if (mergedPos == null || mergedCell == null) {\n      return true;\n    }\n    tr.setNodeMarkup(mergedPos + rect.tableStart, null, {\n      ...addColSpan(\n        mergedCell.attrs,\n        mergedCell.attrs.colspan,\n        rect.right - rect.left - mergedCell.attrs.colspan\n      ),\n      rowspan: rect.bottom - rect.top\n    });\n    if (content.size) {\n      const end = mergedPos + 1 + mergedCell.content.size;\n      const start = isEmpty(mergedCell) ? mergedPos + 1 : end;\n      tr.replaceWith(start + rect.tableStart, end + rect.tableStart, content);\n    }\n    tr.setSelection(\n      new CellSelection(tr.doc.resolve(mergedPos + rect.tableStart))\n    );\n    dispatch(tr);\n  }\n  return true;\n}\nfunction splitCell(state, dispatch) {\n  const nodeTypes = tableNodeTypes(state.schema);\n  return splitCellWithType(({ node }) => {\n    return nodeTypes[node.type.spec.tableRole];\n  })(state, dispatch);\n}\nfunction splitCellWithType(getCellType) {\n  return (state, dispatch) => {\n    var _a;\n    const sel = state.selection;\n    let cellNode;\n    let cellPos;\n    if (!(sel instanceof CellSelection)) {\n      cellNode = cellWrapping(sel.$from);\n      if (!cellNode) return false;\n      cellPos = (_a = cellAround(sel.$from)) == null ? void 0 : _a.pos;\n    } else {\n      if (sel.$anchorCell.pos != sel.$headCell.pos) return false;\n      cellNode = sel.$anchorCell.nodeAfter;\n      cellPos = sel.$anchorCell.pos;\n    }\n    if (cellNode == null || cellPos == null) {\n      return false;\n    }\n    if (cellNode.attrs.colspan == 1 && cellNode.attrs.rowspan == 1) {\n      return false;\n    }\n    if (dispatch) {\n      let baseAttrs = cellNode.attrs;\n      const attrs = [];\n      const colwidth = baseAttrs.colwidth;\n      if (baseAttrs.rowspan > 1) baseAttrs = { ...baseAttrs, rowspan: 1 };\n      if (baseAttrs.colspan > 1) baseAttrs = { ...baseAttrs, colspan: 1 };\n      const rect = selectedRect(state), tr = state.tr;\n      for (let i = 0; i < rect.right - rect.left; i++)\n        attrs.push(\n          colwidth ? {\n            ...baseAttrs,\n            colwidth: colwidth && colwidth[i] ? [colwidth[i]] : null\n          } : baseAttrs\n        );\n      let lastCell;\n      for (let row = rect.top; row < rect.bottom; row++) {\n        let pos = rect.map.positionAt(row, rect.left, rect.table);\n        if (row == rect.top) pos += cellNode.nodeSize;\n        for (let col = rect.left, i = 0; col < rect.right; col++, i++) {\n          if (col == rect.left && row == rect.top) continue;\n          tr.insert(\n            lastCell = tr.mapping.map(pos + rect.tableStart, 1),\n            getCellType({ node: cellNode, row, col }).createAndFill(attrs[i])\n          );\n        }\n      }\n      tr.setNodeMarkup(\n        cellPos,\n        getCellType({ node: cellNode, row: rect.top, col: rect.left }),\n        attrs[0]\n      );\n      if (sel instanceof CellSelection)\n        tr.setSelection(\n          new CellSelection(\n            tr.doc.resolve(sel.$anchorCell.pos),\n            lastCell ? tr.doc.resolve(lastCell) : void 0\n          )\n        );\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction setCellAttr(name, value) {\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    const $cell = selectionCell(state);\n    if ($cell.nodeAfter.attrs[name] === value) return false;\n    if (dispatch) {\n      const tr = state.tr;\n      if (state.selection instanceof CellSelection)\n        state.selection.forEachCell((node, pos) => {\n          if (node.attrs[name] !== value)\n            tr.setNodeMarkup(pos, null, {\n              ...node.attrs,\n              [name]: value\n            });\n        });\n      else\n        tr.setNodeMarkup($cell.pos, null, {\n          ...$cell.nodeAfter.attrs,\n          [name]: value\n        });\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction deprecated_toggleHeader(type) {\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    if (dispatch) {\n      const types = tableNodeTypes(state.schema);\n      const rect = selectedRect(state), tr = state.tr;\n      const cells = rect.map.cellsInRect(\n        type == \"column\" ? {\n          left: rect.left,\n          top: 0,\n          right: rect.right,\n          bottom: rect.map.height\n        } : type == \"row\" ? {\n          left: 0,\n          top: rect.top,\n          right: rect.map.width,\n          bottom: rect.bottom\n        } : rect\n      );\n      const nodes = cells.map((pos) => rect.table.nodeAt(pos));\n      for (let i = 0; i < cells.length; i++)\n        if (nodes[i].type == types.header_cell)\n          tr.setNodeMarkup(\n            rect.tableStart + cells[i],\n            types.cell,\n            nodes[i].attrs\n          );\n      if (tr.steps.length == 0)\n        for (let i = 0; i < cells.length; i++)\n          tr.setNodeMarkup(\n            rect.tableStart + cells[i],\n            types.header_cell,\n            nodes[i].attrs\n          );\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction isHeaderEnabledByType(type, rect, types) {\n  const cellPositions = rect.map.cellsInRect({\n    left: 0,\n    top: 0,\n    right: type == \"row\" ? rect.map.width : 1,\n    bottom: type == \"column\" ? rect.map.height : 1\n  });\n  for (let i = 0; i < cellPositions.length; i++) {\n    const cell = rect.table.nodeAt(cellPositions[i]);\n    if (cell && cell.type !== types.header_cell) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction toggleHeader(type, options) {\n  options = options || { useDeprecatedLogic: false };\n  if (options.useDeprecatedLogic) return deprecated_toggleHeader(type);\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    if (dispatch) {\n      const types = tableNodeTypes(state.schema);\n      const rect = selectedRect(state), tr = state.tr;\n      const isHeaderRowEnabled = isHeaderEnabledByType(\"row\", rect, types);\n      const isHeaderColumnEnabled = isHeaderEnabledByType(\n        \"column\",\n        rect,\n        types\n      );\n      const isHeaderEnabled = type === \"column\" ? isHeaderRowEnabled : type === \"row\" ? isHeaderColumnEnabled : false;\n      const selectionStartsAt = isHeaderEnabled ? 1 : 0;\n      const cellsRect = type == \"column\" ? {\n        left: 0,\n        top: selectionStartsAt,\n        right: 1,\n        bottom: rect.map.height\n      } : type == \"row\" ? {\n        left: selectionStartsAt,\n        top: 0,\n        right: rect.map.width,\n        bottom: 1\n      } : rect;\n      const newType = type == \"column\" ? isHeaderColumnEnabled ? types.cell : types.header_cell : type == \"row\" ? isHeaderRowEnabled ? types.cell : types.header_cell : types.cell;\n      rect.map.cellsInRect(cellsRect).forEach((relativeCellPos) => {\n        const cellPos = relativeCellPos + rect.tableStart;\n        const cell = tr.doc.nodeAt(cellPos);\n        if (cell) {\n          tr.setNodeMarkup(cellPos, newType, cell.attrs);\n        }\n      });\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nvar toggleHeaderRow = toggleHeader(\"row\", {\n  useDeprecatedLogic: true\n});\nvar toggleHeaderColumn = toggleHeader(\"column\", {\n  useDeprecatedLogic: true\n});\nvar toggleHeaderCell = toggleHeader(\"cell\", {\n  useDeprecatedLogic: true\n});\nfunction findNextCell($cell, dir) {\n  if (dir < 0) {\n    const before = $cell.nodeBefore;\n    if (before) return $cell.pos - before.nodeSize;\n    for (let row = $cell.index(-1) - 1, rowEnd = $cell.before(); row >= 0; row--) {\n      const rowNode = $cell.node(-1).child(row);\n      const lastChild = rowNode.lastChild;\n      if (lastChild) {\n        return rowEnd - 1 - lastChild.nodeSize;\n      }\n      rowEnd -= rowNode.nodeSize;\n    }\n  } else {\n    if ($cell.index() < $cell.parent.childCount - 1) {\n      return $cell.pos + $cell.nodeAfter.nodeSize;\n    }\n    const table = $cell.node(-1);\n    for (let row = $cell.indexAfter(-1), rowStart = $cell.after(); row < table.childCount; row++) {\n      const rowNode = table.child(row);\n      if (rowNode.childCount) return rowStart + 1;\n      rowStart += rowNode.nodeSize;\n    }\n  }\n  return null;\n}\nfunction goToNextCell(direction) {\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    const cell = findNextCell(selectionCell(state), direction);\n    if (cell == null) return false;\n    if (dispatch) {\n      const $cell = state.doc.resolve(cell);\n      dispatch(\n        state.tr.setSelection(TextSelection2.between($cell, moveCellForward($cell))).scrollIntoView()\n      );\n    }\n    return true;\n  };\n}\nfunction deleteTable(state, dispatch) {\n  const $pos = state.selection.$anchor;\n  for (let d = $pos.depth; d > 0; d--) {\n    const node = $pos.node(d);\n    if (node.type.spec.tableRole == \"table\") {\n      if (dispatch)\n        dispatch(\n          state.tr.delete($pos.before(d), $pos.after(d)).scrollIntoView()\n        );\n      return true;\n    }\n  }\n  return false;\n}\nfunction deleteCellSelection(state, dispatch) {\n  const sel = state.selection;\n  if (!(sel instanceof CellSelection)) return false;\n  if (dispatch) {\n    const tr = state.tr;\n    const baseContent = tableNodeTypes(state.schema).cell.createAndFill().content;\n    sel.forEachCell((cell, pos) => {\n      if (!cell.content.eq(baseContent))\n        tr.replace(\n          tr.mapping.map(pos + 1),\n          tr.mapping.map(pos + cell.nodeSize - 1),\n          new Slice2(baseContent, 0, 0)\n        );\n    });\n    if (tr.docChanged) dispatch(tr);\n  }\n  return true;\n}\n\n// src/copypaste.ts\nimport { Fragment as Fragment3, Slice as Slice3 } from \"prosemirror-model\";\nimport { Transform } from \"prosemirror-transform\";\nfunction pastedCells(slice) {\n  if (!slice.size) return null;\n  let { content, openStart, openEnd } = slice;\n  while (content.childCount == 1 && (openStart > 0 && openEnd > 0 || content.child(0).type.spec.tableRole == \"table\")) {\n    openStart--;\n    openEnd--;\n    content = content.child(0).content;\n  }\n  const first = content.child(0);\n  const role = first.type.spec.tableRole;\n  const schema = first.type.schema, rows = [];\n  if (role == \"row\") {\n    for (let i = 0; i < content.childCount; i++) {\n      let cells = content.child(i).content;\n      const left = i ? 0 : Math.max(0, openStart - 1);\n      const right = i < content.childCount - 1 ? 0 : Math.max(0, openEnd - 1);\n      if (left || right)\n        cells = fitSlice(\n          tableNodeTypes(schema).row,\n          new Slice3(cells, left, right)\n        ).content;\n      rows.push(cells);\n    }\n  } else if (role == \"cell\" || role == \"header_cell\") {\n    rows.push(\n      openStart || openEnd ? fitSlice(\n        tableNodeTypes(schema).row,\n        new Slice3(content, openStart, openEnd)\n      ).content : content\n    );\n  } else {\n    return null;\n  }\n  return ensureRectangular(schema, rows);\n}\nfunction ensureRectangular(schema, rows) {\n  const widths = [];\n  for (let i = 0; i < rows.length; i++) {\n    const row = rows[i];\n    for (let j = row.childCount - 1; j >= 0; j--) {\n      const { rowspan, colspan } = row.child(j).attrs;\n      for (let r = i; r < i + rowspan; r++)\n        widths[r] = (widths[r] || 0) + colspan;\n    }\n  }\n  let width = 0;\n  for (let r = 0; r < widths.length; r++) width = Math.max(width, widths[r]);\n  for (let r = 0; r < widths.length; r++) {\n    if (r >= rows.length) rows.push(Fragment3.empty);\n    if (widths[r] < width) {\n      const empty = tableNodeTypes(schema).cell.createAndFill();\n      const cells = [];\n      for (let i = widths[r]; i < width; i++) {\n        cells.push(empty);\n      }\n      rows[r] = rows[r].append(Fragment3.from(cells));\n    }\n  }\n  return { height: rows.length, width, rows };\n}\nfunction fitSlice(nodeType, slice) {\n  const node = nodeType.createAndFill();\n  const tr = new Transform(node).replace(0, node.content.size, slice);\n  return tr.doc;\n}\nfunction clipCells({ width, height, rows }, newWidth, newHeight) {\n  if (width != newWidth) {\n    const added = [];\n    const newRows = [];\n    for (let row = 0; row < rows.length; row++) {\n      const frag = rows[row], cells = [];\n      for (let col = added[row] || 0, i = 0; col < newWidth; i++) {\n        let cell = frag.child(i % frag.childCount);\n        if (col + cell.attrs.colspan > newWidth)\n          cell = cell.type.createChecked(\n            removeColSpan(\n              cell.attrs,\n              cell.attrs.colspan,\n              col + cell.attrs.colspan - newWidth\n            ),\n            cell.content\n          );\n        cells.push(cell);\n        col += cell.attrs.colspan;\n        for (let j = 1; j < cell.attrs.rowspan; j++)\n          added[row + j] = (added[row + j] || 0) + cell.attrs.colspan;\n      }\n      newRows.push(Fragment3.from(cells));\n    }\n    rows = newRows;\n    width = newWidth;\n  }\n  if (height != newHeight) {\n    const newRows = [];\n    for (let row = 0, i = 0; row < newHeight; row++, i++) {\n      const cells = [], source = rows[i % height];\n      for (let j = 0; j < source.childCount; j++) {\n        let cell = source.child(j);\n        if (row + cell.attrs.rowspan > newHeight)\n          cell = cell.type.create(\n            {\n              ...cell.attrs,\n              rowspan: Math.max(1, newHeight - cell.attrs.rowspan)\n            },\n            cell.content\n          );\n        cells.push(cell);\n      }\n      newRows.push(Fragment3.from(cells));\n    }\n    rows = newRows;\n    height = newHeight;\n  }\n  return { width, height, rows };\n}\nfunction growTable(tr, map, table, start, width, height, mapFrom) {\n  const schema = tr.doc.type.schema;\n  const types = tableNodeTypes(schema);\n  let empty;\n  let emptyHead;\n  if (width > map.width) {\n    for (let row = 0, rowEnd = 0; row < map.height; row++) {\n      const rowNode = table.child(row);\n      rowEnd += rowNode.nodeSize;\n      const cells = [];\n      let add;\n      if (rowNode.lastChild == null || rowNode.lastChild.type == types.cell)\n        add = empty || (empty = types.cell.createAndFill());\n      else add = emptyHead || (emptyHead = types.header_cell.createAndFill());\n      for (let i = map.width; i < width; i++) cells.push(add);\n      tr.insert(tr.mapping.slice(mapFrom).map(rowEnd - 1 + start), cells);\n    }\n  }\n  if (height > map.height) {\n    const cells = [];\n    for (let i = 0, start2 = (map.height - 1) * map.width; i < Math.max(map.width, width); i++) {\n      const header = i >= map.width ? false : table.nodeAt(map.map[start2 + i]).type == types.header_cell;\n      cells.push(\n        header ? emptyHead || (emptyHead = types.header_cell.createAndFill()) : empty || (empty = types.cell.createAndFill())\n      );\n    }\n    const emptyRow = types.row.create(null, Fragment3.from(cells)), rows = [];\n    for (let i = map.height; i < height; i++) rows.push(emptyRow);\n    tr.insert(tr.mapping.slice(mapFrom).map(start + table.nodeSize - 2), rows);\n  }\n  return !!(empty || emptyHead);\n}\nfunction isolateHorizontal(tr, map, table, start, left, right, top, mapFrom) {\n  if (top == 0 || top == map.height) return false;\n  let found = false;\n  for (let col = left; col < right; col++) {\n    const index = top * map.width + col, pos = map.map[index];\n    if (map.map[index - map.width] == pos) {\n      found = true;\n      const cell = table.nodeAt(pos);\n      const { top: cellTop, left: cellLeft } = map.findCell(pos);\n      tr.setNodeMarkup(tr.mapping.slice(mapFrom).map(pos + start), null, {\n        ...cell.attrs,\n        rowspan: top - cellTop\n      });\n      tr.insert(\n        tr.mapping.slice(mapFrom).map(map.positionAt(top, cellLeft, table)),\n        cell.type.createAndFill({\n          ...cell.attrs,\n          rowspan: cellTop + cell.attrs.rowspan - top\n        })\n      );\n      col += cell.attrs.colspan - 1;\n    }\n  }\n  return found;\n}\nfunction isolateVertical(tr, map, table, start, top, bottom, left, mapFrom) {\n  if (left == 0 || left == map.width) return false;\n  let found = false;\n  for (let row = top; row < bottom; row++) {\n    const index = row * map.width + left, pos = map.map[index];\n    if (map.map[index - 1] == pos) {\n      found = true;\n      const cell = table.nodeAt(pos);\n      const cellLeft = map.colCount(pos);\n      const updatePos = tr.mapping.slice(mapFrom).map(pos + start);\n      tr.setNodeMarkup(\n        updatePos,\n        null,\n        removeColSpan(\n          cell.attrs,\n          left - cellLeft,\n          cell.attrs.colspan - (left - cellLeft)\n        )\n      );\n      tr.insert(\n        updatePos + cell.nodeSize,\n        cell.type.createAndFill(\n          removeColSpan(cell.attrs, 0, left - cellLeft)\n        )\n      );\n      row += cell.attrs.rowspan - 1;\n    }\n  }\n  return found;\n}\nfunction insertCells(state, dispatch, tableStart, rect, cells) {\n  let table = tableStart ? state.doc.nodeAt(tableStart - 1) : state.doc;\n  if (!table) {\n    throw new Error(\"No table found\");\n  }\n  let map = TableMap.get(table);\n  const { top, left } = rect;\n  const right = left + cells.width, bottom = top + cells.height;\n  const tr = state.tr;\n  let mapFrom = 0;\n  function recomp() {\n    table = tableStart ? tr.doc.nodeAt(tableStart - 1) : tr.doc;\n    if (!table) {\n      throw new Error(\"No table found\");\n    }\n    map = TableMap.get(table);\n    mapFrom = tr.mapping.maps.length;\n  }\n  if (growTable(tr, map, table, tableStart, right, bottom, mapFrom)) recomp();\n  if (isolateHorizontal(tr, map, table, tableStart, left, right, top, mapFrom))\n    recomp();\n  if (isolateHorizontal(tr, map, table, tableStart, left, right, bottom, mapFrom))\n    recomp();\n  if (isolateVertical(tr, map, table, tableStart, top, bottom, left, mapFrom))\n    recomp();\n  if (isolateVertical(tr, map, table, tableStart, top, bottom, right, mapFrom))\n    recomp();\n  for (let row = top; row < bottom; row++) {\n    const from = map.positionAt(row, left, table), to = map.positionAt(row, right, table);\n    tr.replace(\n      tr.mapping.slice(mapFrom).map(from + tableStart),\n      tr.mapping.slice(mapFrom).map(to + tableStart),\n      new Slice3(cells.rows[row - top], 0, 0)\n    );\n  }\n  recomp();\n  tr.setSelection(\n    new CellSelection(\n      tr.doc.resolve(tableStart + map.positionAt(top, left, table)),\n      tr.doc.resolve(tableStart + map.positionAt(bottom - 1, right - 1, table))\n    )\n  );\n  dispatch(tr);\n}\n\n// src/input.ts\nvar handleKeyDown = keydownHandler({\n  ArrowLeft: arrow(\"horiz\", -1),\n  ArrowRight: arrow(\"horiz\", 1),\n  ArrowUp: arrow(\"vert\", -1),\n  ArrowDown: arrow(\"vert\", 1),\n  \"Shift-ArrowLeft\": shiftArrow(\"horiz\", -1),\n  \"Shift-ArrowRight\": shiftArrow(\"horiz\", 1),\n  \"Shift-ArrowUp\": shiftArrow(\"vert\", -1),\n  \"Shift-ArrowDown\": shiftArrow(\"vert\", 1),\n  Backspace: deleteCellSelection,\n  \"Mod-Backspace\": deleteCellSelection,\n  Delete: deleteCellSelection,\n  \"Mod-Delete\": deleteCellSelection\n});\nfunction maybeSetSelection(state, dispatch, selection) {\n  if (selection.eq(state.selection)) return false;\n  if (dispatch) dispatch(state.tr.setSelection(selection).scrollIntoView());\n  return true;\n}\nfunction arrow(axis, dir) {\n  return (state, dispatch, view) => {\n    if (!view) return false;\n    const sel = state.selection;\n    if (sel instanceof CellSelection) {\n      return maybeSetSelection(\n        state,\n        dispatch,\n        Selection2.near(sel.$headCell, dir)\n      );\n    }\n    if (axis != \"horiz\" && !sel.empty) return false;\n    const end = atEndOfCell(view, axis, dir);\n    if (end == null) return false;\n    if (axis == \"horiz\") {\n      return maybeSetSelection(\n        state,\n        dispatch,\n        Selection2.near(state.doc.resolve(sel.head + dir), dir)\n      );\n    } else {\n      const $cell = state.doc.resolve(end);\n      const $next = nextCell($cell, axis, dir);\n      let newSel;\n      if ($next) newSel = Selection2.near($next, 1);\n      else if (dir < 0)\n        newSel = Selection2.near(state.doc.resolve($cell.before(-1)), -1);\n      else newSel = Selection2.near(state.doc.resolve($cell.after(-1)), 1);\n      return maybeSetSelection(state, dispatch, newSel);\n    }\n  };\n}\nfunction shiftArrow(axis, dir) {\n  return (state, dispatch, view) => {\n    if (!view) return false;\n    const sel = state.selection;\n    let cellSel;\n    if (sel instanceof CellSelection) {\n      cellSel = sel;\n    } else {\n      const end = atEndOfCell(view, axis, dir);\n      if (end == null) return false;\n      cellSel = new CellSelection(state.doc.resolve(end));\n    }\n    const $head = nextCell(cellSel.$headCell, axis, dir);\n    if (!$head) return false;\n    return maybeSetSelection(\n      state,\n      dispatch,\n      new CellSelection(cellSel.$anchorCell, $head)\n    );\n  };\n}\nfunction handleTripleClick(view, pos) {\n  const doc = view.state.doc, $cell = cellAround(doc.resolve(pos));\n  if (!$cell) return false;\n  view.dispatch(view.state.tr.setSelection(new CellSelection($cell)));\n  return true;\n}\nfunction handlePaste(view, _, slice) {\n  if (!isInTable(view.state)) return false;\n  let cells = pastedCells(slice);\n  const sel = view.state.selection;\n  if (sel instanceof CellSelection) {\n    if (!cells)\n      cells = {\n        width: 1,\n        height: 1,\n        rows: [\n          Fragment4.from(\n            fitSlice(tableNodeTypes(view.state.schema).cell, slice)\n          )\n        ]\n      };\n    const table = sel.$anchorCell.node(-1);\n    const start = sel.$anchorCell.start(-1);\n    const rect = TableMap.get(table).rectBetween(\n      sel.$anchorCell.pos - start,\n      sel.$headCell.pos - start\n    );\n    cells = clipCells(cells, rect.right - rect.left, rect.bottom - rect.top);\n    insertCells(view.state, view.dispatch, start, rect, cells);\n    return true;\n  } else if (cells) {\n    const $cell = selectionCell(view.state);\n    const start = $cell.start(-1);\n    insertCells(\n      view.state,\n      view.dispatch,\n      start,\n      TableMap.get($cell.node(-1)).findCell($cell.pos - start),\n      cells\n    );\n    return true;\n  } else {\n    return false;\n  }\n}\nfunction handleMouseDown(view, startEvent) {\n  var _a;\n  if (startEvent.ctrlKey || startEvent.metaKey) return;\n  const startDOMCell = domInCell(view, startEvent.target);\n  let $anchor;\n  if (startEvent.shiftKey && view.state.selection instanceof CellSelection) {\n    setCellSelection(view.state.selection.$anchorCell, startEvent);\n    startEvent.preventDefault();\n  } else if (startEvent.shiftKey && startDOMCell && ($anchor = cellAround(view.state.selection.$anchor)) != null && ((_a = cellUnderMouse(view, startEvent)) == null ? void 0 : _a.pos) != $anchor.pos) {\n    setCellSelection($anchor, startEvent);\n    startEvent.preventDefault();\n  } else if (!startDOMCell) {\n    return;\n  }\n  function setCellSelection($anchor2, event) {\n    let $head = cellUnderMouse(view, event);\n    const starting = tableEditingKey.getState(view.state) == null;\n    if (!$head || !inSameTable($anchor2, $head)) {\n      if (starting) $head = $anchor2;\n      else return;\n    }\n    const selection = new CellSelection($anchor2, $head);\n    if (starting || !view.state.selection.eq(selection)) {\n      const tr = view.state.tr.setSelection(selection);\n      if (starting) tr.setMeta(tableEditingKey, $anchor2.pos);\n      view.dispatch(tr);\n    }\n  }\n  function stop() {\n    view.root.removeEventListener(\"mouseup\", stop);\n    view.root.removeEventListener(\"dragstart\", stop);\n    view.root.removeEventListener(\"mousemove\", move);\n    if (tableEditingKey.getState(view.state) != null)\n      view.dispatch(view.state.tr.setMeta(tableEditingKey, -1));\n  }\n  function move(_event) {\n    const event = _event;\n    const anchor = tableEditingKey.getState(view.state);\n    let $anchor2;\n    if (anchor != null) {\n      $anchor2 = view.state.doc.resolve(anchor);\n    } else if (domInCell(view, event.target) != startDOMCell) {\n      $anchor2 = cellUnderMouse(view, startEvent);\n      if (!$anchor2) return stop();\n    }\n    if ($anchor2) setCellSelection($anchor2, event);\n  }\n  view.root.addEventListener(\"mouseup\", stop);\n  view.root.addEventListener(\"dragstart\", stop);\n  view.root.addEventListener(\"mousemove\", move);\n}\nfunction atEndOfCell(view, axis, dir) {\n  if (!(view.state.selection instanceof TextSelection3)) return null;\n  const { $head } = view.state.selection;\n  for (let d = $head.depth - 1; d >= 0; d--) {\n    const parent = $head.node(d), index = dir < 0 ? $head.index(d) : $head.indexAfter(d);\n    if (index != (dir < 0 ? 0 : parent.childCount)) return null;\n    if (parent.type.spec.tableRole == \"cell\" || parent.type.spec.tableRole == \"header_cell\") {\n      const cellPos = $head.before(d);\n      const dirStr = axis == \"vert\" ? dir > 0 ? \"down\" : \"up\" : dir > 0 ? \"right\" : \"left\";\n      return view.endOfTextblock(dirStr) ? cellPos : null;\n    }\n  }\n  return null;\n}\nfunction domInCell(view, dom) {\n  for (; dom && dom != view.dom; dom = dom.parentNode) {\n    if (dom.nodeName == \"TD\" || dom.nodeName == \"TH\") {\n      return dom;\n    }\n  }\n  return null;\n}\nfunction cellUnderMouse(view, event) {\n  const mousePos = view.posAtCoords({\n    left: event.clientX,\n    top: event.clientY\n  });\n  if (!mousePos) return null;\n  return mousePos ? cellAround(view.state.doc.resolve(mousePos.pos)) : null;\n}\n\n// src/columnresizing.ts\nimport { Plugin, PluginKey as PluginKey3 } from \"prosemirror-state\";\nimport {\n  Decoration as Decoration2,\n  DecorationSet as DecorationSet2\n} from \"prosemirror-view\";\n\n// src/tableview.ts\nvar TableView = class {\n  constructor(node, defaultCellMinWidth) {\n    this.node = node;\n    this.defaultCellMinWidth = defaultCellMinWidth;\n    this.dom = document.createElement(\"div\");\n    this.dom.className = \"tableWrapper\";\n    this.table = this.dom.appendChild(document.createElement(\"table\"));\n    this.table.style.setProperty(\n      \"--default-cell-min-width\",\n      `${defaultCellMinWidth}px`\n    );\n    this.colgroup = this.table.appendChild(document.createElement(\"colgroup\"));\n    updateColumnsOnResize(node, this.colgroup, this.table, defaultCellMinWidth);\n    this.contentDOM = this.table.appendChild(document.createElement(\"tbody\"));\n  }\n  update(node) {\n    if (node.type != this.node.type) return false;\n    this.node = node;\n    updateColumnsOnResize(\n      node,\n      this.colgroup,\n      this.table,\n      this.defaultCellMinWidth\n    );\n    return true;\n  }\n  ignoreMutation(record) {\n    return record.type == \"attributes\" && (record.target == this.table || this.colgroup.contains(record.target));\n  }\n};\nfunction updateColumnsOnResize(node, colgroup, table, defaultCellMinWidth, overrideCol, overrideValue) {\n  var _a;\n  let totalWidth = 0;\n  let fixedWidth = true;\n  let nextDOM = colgroup.firstChild;\n  const row = node.firstChild;\n  if (!row) return;\n  for (let i = 0, col = 0; i < row.childCount; i++) {\n    const { colspan, colwidth } = row.child(i).attrs;\n    for (let j = 0; j < colspan; j++, col++) {\n      const hasWidth = overrideCol == col ? overrideValue : colwidth && colwidth[j];\n      const cssWidth = hasWidth ? hasWidth + \"px\" : \"\";\n      totalWidth += hasWidth || defaultCellMinWidth;\n      if (!hasWidth) fixedWidth = false;\n      if (!nextDOM) {\n        const col2 = document.createElement(\"col\");\n        col2.style.width = cssWidth;\n        colgroup.appendChild(col2);\n      } else {\n        if (nextDOM.style.width != cssWidth) {\n          nextDOM.style.width = cssWidth;\n        }\n        nextDOM = nextDOM.nextSibling;\n      }\n    }\n  }\n  while (nextDOM) {\n    const after = nextDOM.nextSibling;\n    (_a = nextDOM.parentNode) == null ? void 0 : _a.removeChild(nextDOM);\n    nextDOM = after;\n  }\n  if (fixedWidth) {\n    table.style.width = totalWidth + \"px\";\n    table.style.minWidth = \"\";\n  } else {\n    table.style.width = \"\";\n    table.style.minWidth = totalWidth + \"px\";\n  }\n}\n\n// src/columnresizing.ts\nvar columnResizingPluginKey = new PluginKey3(\n  \"tableColumnResizing\"\n);\nfunction columnResizing({\n  handleWidth = 5,\n  cellMinWidth = 25,\n  defaultCellMinWidth = 100,\n  View = TableView,\n  lastColumnResizable = true\n} = {}) {\n  const plugin = new Plugin({\n    key: columnResizingPluginKey,\n    state: {\n      init(_, state) {\n        var _a, _b;\n        const nodeViews = (_b = (_a = plugin.spec) == null ? void 0 : _a.props) == null ? void 0 : _b.nodeViews;\n        const tableName = tableNodeTypes(state.schema).table.name;\n        if (View && nodeViews) {\n          nodeViews[tableName] = (node, view) => {\n            return new View(node, defaultCellMinWidth, view);\n          };\n        }\n        return new ResizeState(-1, false);\n      },\n      apply(tr, prev) {\n        return prev.apply(tr);\n      }\n    },\n    props: {\n      attributes: (state) => {\n        const pluginState = columnResizingPluginKey.getState(state);\n        return pluginState && pluginState.activeHandle > -1 ? { class: \"resize-cursor\" } : {};\n      },\n      handleDOMEvents: {\n        mousemove: (view, event) => {\n          handleMouseMove(view, event, handleWidth, lastColumnResizable);\n        },\n        mouseleave: (view) => {\n          handleMouseLeave(view);\n        },\n        mousedown: (view, event) => {\n          handleMouseDown2(view, event, cellMinWidth, defaultCellMinWidth);\n        }\n      },\n      decorations: (state) => {\n        const pluginState = columnResizingPluginKey.getState(state);\n        if (pluginState && pluginState.activeHandle > -1) {\n          return handleDecorations(state, pluginState.activeHandle);\n        }\n      },\n      nodeViews: {}\n    }\n  });\n  return plugin;\n}\nvar ResizeState = class _ResizeState {\n  constructor(activeHandle, dragging) {\n    this.activeHandle = activeHandle;\n    this.dragging = dragging;\n  }\n  apply(tr) {\n    const state = this;\n    const action = tr.getMeta(columnResizingPluginKey);\n    if (action && action.setHandle != null)\n      return new _ResizeState(action.setHandle, false);\n    if (action && action.setDragging !== void 0)\n      return new _ResizeState(state.activeHandle, action.setDragging);\n    if (state.activeHandle > -1 && tr.docChanged) {\n      let handle = tr.mapping.map(state.activeHandle, -1);\n      if (!pointsAtCell(tr.doc.resolve(handle))) {\n        handle = -1;\n      }\n      return new _ResizeState(handle, state.dragging);\n    }\n    return state;\n  }\n};\nfunction handleMouseMove(view, event, handleWidth, lastColumnResizable) {\n  if (!view.editable) return;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (!pluginState) return;\n  if (!pluginState.dragging) {\n    const target = domCellAround(event.target);\n    let cell = -1;\n    if (target) {\n      const { left, right } = target.getBoundingClientRect();\n      if (event.clientX - left <= handleWidth)\n        cell = edgeCell(view, event, \"left\", handleWidth);\n      else if (right - event.clientX <= handleWidth)\n        cell = edgeCell(view, event, \"right\", handleWidth);\n    }\n    if (cell != pluginState.activeHandle) {\n      if (!lastColumnResizable && cell !== -1) {\n        const $cell = view.state.doc.resolve(cell);\n        const table = $cell.node(-1);\n        const map = TableMap.get(table);\n        const tableStart = $cell.start(-1);\n        const col = map.colCount($cell.pos - tableStart) + $cell.nodeAfter.attrs.colspan - 1;\n        if (col == map.width - 1) {\n          return;\n        }\n      }\n      updateHandle(view, cell);\n    }\n  }\n}\nfunction handleMouseLeave(view) {\n  if (!view.editable) return;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (pluginState && pluginState.activeHandle > -1 && !pluginState.dragging)\n    updateHandle(view, -1);\n}\nfunction handleMouseDown2(view, event, cellMinWidth, defaultCellMinWidth) {\n  var _a;\n  if (!view.editable) return false;\n  const win = (_a = view.dom.ownerDocument.defaultView) != null ? _a : window;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (!pluginState || pluginState.activeHandle == -1 || pluginState.dragging)\n    return false;\n  const cell = view.state.doc.nodeAt(pluginState.activeHandle);\n  const width = currentColWidth(view, pluginState.activeHandle, cell.attrs);\n  view.dispatch(\n    view.state.tr.setMeta(columnResizingPluginKey, {\n      setDragging: { startX: event.clientX, startWidth: width }\n    })\n  );\n  function finish(event2) {\n    win.removeEventListener(\"mouseup\", finish);\n    win.removeEventListener(\"mousemove\", move);\n    const pluginState2 = columnResizingPluginKey.getState(view.state);\n    if (pluginState2 == null ? void 0 : pluginState2.dragging) {\n      updateColumnWidth(\n        view,\n        pluginState2.activeHandle,\n        draggedWidth(pluginState2.dragging, event2, cellMinWidth)\n      );\n      view.dispatch(\n        view.state.tr.setMeta(columnResizingPluginKey, { setDragging: null })\n      );\n    }\n  }\n  function move(event2) {\n    if (!event2.which) return finish(event2);\n    const pluginState2 = columnResizingPluginKey.getState(view.state);\n    if (!pluginState2) return;\n    if (pluginState2.dragging) {\n      const dragged = draggedWidth(pluginState2.dragging, event2, cellMinWidth);\n      displayColumnWidth(\n        view,\n        pluginState2.activeHandle,\n        dragged,\n        defaultCellMinWidth\n      );\n    }\n  }\n  displayColumnWidth(\n    view,\n    pluginState.activeHandle,\n    width,\n    defaultCellMinWidth\n  );\n  win.addEventListener(\"mouseup\", finish);\n  win.addEventListener(\"mousemove\", move);\n  event.preventDefault();\n  return true;\n}\nfunction currentColWidth(view, cellPos, { colspan, colwidth }) {\n  const width = colwidth && colwidth[colwidth.length - 1];\n  if (width) return width;\n  const dom = view.domAtPos(cellPos);\n  const node = dom.node.childNodes[dom.offset];\n  let domWidth = node.offsetWidth, parts = colspan;\n  if (colwidth) {\n    for (let i = 0; i < colspan; i++)\n      if (colwidth[i]) {\n        domWidth -= colwidth[i];\n        parts--;\n      }\n  }\n  return domWidth / parts;\n}\nfunction domCellAround(target) {\n  while (target && target.nodeName != \"TD\" && target.nodeName != \"TH\")\n    target = target.classList && target.classList.contains(\"ProseMirror\") ? null : target.parentNode;\n  return target;\n}\nfunction edgeCell(view, event, side, handleWidth) {\n  const offset = side == \"right\" ? -handleWidth : handleWidth;\n  const found = view.posAtCoords({\n    left: event.clientX + offset,\n    top: event.clientY\n  });\n  if (!found) return -1;\n  const { pos } = found;\n  const $cell = cellAround(view.state.doc.resolve(pos));\n  if (!$cell) return -1;\n  if (side == \"right\") return $cell.pos;\n  const map = TableMap.get($cell.node(-1)), start = $cell.start(-1);\n  const index = map.map.indexOf($cell.pos - start);\n  return index % map.width == 0 ? -1 : start + map.map[index - 1];\n}\nfunction draggedWidth(dragging, event, resizeMinWidth) {\n  const offset = event.clientX - dragging.startX;\n  return Math.max(resizeMinWidth, dragging.startWidth + offset);\n}\nfunction updateHandle(view, value) {\n  view.dispatch(\n    view.state.tr.setMeta(columnResizingPluginKey, { setHandle: value })\n  );\n}\nfunction updateColumnWidth(view, cell, width) {\n  const $cell = view.state.doc.resolve(cell);\n  const table = $cell.node(-1), map = TableMap.get(table), start = $cell.start(-1);\n  const col = map.colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  const tr = view.state.tr;\n  for (let row = 0; row < map.height; row++) {\n    const mapIndex = row * map.width + col;\n    if (row && map.map[mapIndex] == map.map[mapIndex - map.width]) continue;\n    const pos = map.map[mapIndex];\n    const attrs = table.nodeAt(pos).attrs;\n    const index = attrs.colspan == 1 ? 0 : col - map.colCount(pos);\n    if (attrs.colwidth && attrs.colwidth[index] == width) continue;\n    const colwidth = attrs.colwidth ? attrs.colwidth.slice() : zeroes(attrs.colspan);\n    colwidth[index] = width;\n    tr.setNodeMarkup(start + pos, null, { ...attrs, colwidth });\n  }\n  if (tr.docChanged) view.dispatch(tr);\n}\nfunction displayColumnWidth(view, cell, width, defaultCellMinWidth) {\n  const $cell = view.state.doc.resolve(cell);\n  const table = $cell.node(-1), start = $cell.start(-1);\n  const col = TableMap.get(table).colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  let dom = view.domAtPos($cell.start(-1)).node;\n  while (dom && dom.nodeName != \"TABLE\") {\n    dom = dom.parentNode;\n  }\n  if (!dom) return;\n  updateColumnsOnResize(\n    table,\n    dom.firstChild,\n    dom,\n    defaultCellMinWidth,\n    col,\n    width\n  );\n}\nfunction zeroes(n) {\n  return Array(n).fill(0);\n}\nfunction handleDecorations(state, cell) {\n  var _a;\n  const decorations = [];\n  const $cell = state.doc.resolve(cell);\n  const table = $cell.node(-1);\n  if (!table) {\n    return DecorationSet2.empty;\n  }\n  const map = TableMap.get(table);\n  const start = $cell.start(-1);\n  const col = map.colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  for (let row = 0; row < map.height; row++) {\n    const index = col + row * map.width;\n    if ((col == map.width - 1 || map.map[index] != map.map[index + 1]) && (row == 0 || map.map[index] != map.map[index - map.width])) {\n      const cellPos = map.map[index];\n      const pos = start + cellPos + table.nodeAt(cellPos).nodeSize - 1;\n      const dom = document.createElement(\"div\");\n      dom.className = \"column-resize-handle\";\n      if ((_a = columnResizingPluginKey.getState(state)) == null ? void 0 : _a.dragging) {\n        decorations.push(\n          Decoration2.node(\n            start + cellPos,\n            start + cellPos + table.nodeAt(cellPos).nodeSize,\n            {\n              class: \"column-resize-dragging\"\n            }\n          )\n        );\n      }\n      decorations.push(Decoration2.widget(pos, dom));\n    }\n  }\n  return DecorationSet2.create(state.doc, decorations);\n}\n\n// src/index.ts\nfunction tableEditing({\n  allowTableNodeSelection = false\n} = {}) {\n  return new Plugin2({\n    key: tableEditingKey,\n    // This piece of state is used to remember when a mouse-drag\n    // cell-selection is happening, so that it can continue even as\n    // transactions (which might move its anchor cell) come in.\n    state: {\n      init() {\n        return null;\n      },\n      apply(tr, cur) {\n        const set = tr.getMeta(tableEditingKey);\n        if (set != null) return set == -1 ? null : set;\n        if (cur == null || !tr.docChanged) return cur;\n        const { deleted, pos } = tr.mapping.mapResult(cur);\n        return deleted ? null : pos;\n      }\n    },\n    props: {\n      decorations: drawCellSelection,\n      handleDOMEvents: {\n        mousedown: handleMouseDown\n      },\n      createSelectionBetween(view) {\n        return tableEditingKey.getState(view.state) != null ? view.state.selection : null;\n      },\n      handleTripleClick,\n      handleKeyDown,\n      handlePaste\n    },\n    appendTransaction(_, oldState, state) {\n      return normalizeSelection(\n        state,\n        fixTables(state, oldState),\n        allowTableNodeSelection\n      );\n    }\n  });\n}\nexport {\n  CellBookmark,\n  CellSelection,\n  ResizeState,\n  TableMap,\n  TableView,\n  clipCells as __clipCells,\n  insertCells as __insertCells,\n  pastedCells as __pastedCells,\n  addColSpan,\n  addColumn,\n  addColumnAfter,\n  addColumnBefore,\n  addRow,\n  addRowAfter,\n  addRowBefore,\n  cellAround,\n  cellNear,\n  colCount,\n  columnIsHeader,\n  columnResizing,\n  columnResizingPluginKey,\n  deleteCellSelection,\n  deleteColumn,\n  deleteRow,\n  deleteTable,\n  findCell,\n  fixTables,\n  fixTablesKey,\n  goToNextCell,\n  handlePaste,\n  inSameTable,\n  isInTable,\n  mergeCells,\n  moveCellForward,\n  nextCell,\n  pointsAtCell,\n  removeColSpan,\n  removeColumn,\n  removeRow,\n  rowIsHeader,\n  selectedRect,\n  selectionCell,\n  setCellAttr,\n  splitCell,\n  splitCellWithType,\n  tableEditing,\n  tableEditingKey,\n  tableNodeTypes,\n  tableNodes,\n  toggleHeader,\n  toggleHeaderCell,\n  toggleHeaderColumn,\n  toggleHeaderRow,\n  updateColumnsOnResize\n};\n", "export function getColStyleDeclaration(minWidth: number, width: number | undefined): [string, string] {\n  if (width) {\n    // apply the stored width unless it is below the configured minimum cell width\n    return ['width', `${Math.max(width, minWidth)}px`]\n  }\n\n  // set the minimum with on the column if it has no stored width\n  return ['min-width', `${minWidth}px`]\n\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { NodeView, ViewMutationRecord } from '@tiptap/pm/view'\n\nimport { getColStyleDeclaration } from './utilities/colStyle.js'\n\nexport function updateColumns(\n  node: ProseMirrorNode,\n  colgroup: HTMLTableColElement, // <colgroup> has the same prototype as <col>\n  table: HTMLTableElement,\n  cellMinWidth: number,\n  overrideCol?: number,\n  overrideValue?: number,\n) {\n  let totalWidth = 0\n  let fixedWidth = true\n  let nextDOM = colgroup.firstChild\n  const row = node.firstChild\n\n  if (row !== null) {\n    for (let i = 0, col = 0; i < row.childCount; i += 1) {\n      const { colspan, colwidth } = row.child(i).attrs\n\n      for (let j = 0; j < colspan; j += 1, col += 1) {\n        const hasWidth = overrideCol === col ? overrideValue : (colwidth && colwidth[j]) as number | undefined\n        const cssWidth = hasWidth ? `${hasWidth}px` : ''\n\n        totalWidth += hasWidth || cellMinWidth\n\n        if (!hasWidth) {\n          fixedWidth = false\n        }\n\n        if (!nextDOM) {\n          const colElement = document.createElement('col')\n\n          const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth)\n\n          colElement.style.setProperty(propertyKey, propertyValue)\n\n          colgroup.appendChild(colElement)\n        } else {\n          if ((nextDOM as HTMLTableColElement).style.width !== cssWidth) {\n            const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth);\n\n            (nextDOM as HTMLTableColElement).style.setProperty(propertyKey, propertyValue)\n          }\n\n          nextDOM = nextDOM.nextSibling\n        }\n      }\n    }\n  }\n\n  while (nextDOM) {\n    const after = nextDOM.nextSibling\n\n    nextDOM.parentNode?.removeChild(nextDOM)\n    nextDOM = after\n  }\n\n  if (fixedWidth) {\n    table.style.width = `${totalWidth}px`\n    table.style.minWidth = ''\n  } else {\n    table.style.width = ''\n    table.style.minWidth = `${totalWidth}px`\n  }\n}\n\nexport class TableView implements NodeView {\n  node: ProseMirrorNode\n\n  cellMinWidth: number\n\n  dom: HTMLDivElement\n\n  table: HTMLTableElement\n\n  colgroup: HTMLTableColElement\n\n  contentDOM: HTMLTableSectionElement\n\n  constructor(node: ProseMirrorNode, cellMinWidth: number) {\n    this.node = node\n    this.cellMinWidth = cellMinWidth\n    this.dom = document.createElement('div')\n    this.dom.className = 'tableWrapper'\n    this.table = this.dom.appendChild(document.createElement('table'))\n    this.colgroup = this.table.appendChild(document.createElement('colgroup'))\n    updateColumns(node, this.colgroup, this.table, cellMinWidth)\n    this.contentDOM = this.table.appendChild(document.createElement('tbody'))\n  }\n\n  update(node: ProseMirrorNode) {\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    this.node = node\n    updateColumns(node, this.colgroup, this.table, this.cellMinWidth)\n\n    return true\n  }\n\n  ignoreMutation(mutation: ViewMutationRecord) {\n    return (\n      mutation.type === 'attributes'\n      && (mutation.target === this.table || this.colgroup.contains(mutation.target))\n    )\n  }\n}\n", "import { DOMOutputSpec, Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { getColStyleDeclaration } from './colStyle.js'\n\nexport type ColGroup = {\n  colgroup: DOMOutputSpec\n  tableWidth: string\n  tableMinWidth: string\n} | Record<string, never>;\n\n/**\n * Creates a colgroup element for a table node in ProseMirror.\n *\n * @param node - The ProseMirror node representing the table.\n * @param cellMinWidth - The minimum width of a cell in the table.\n * @param overrideCol - (Optional) The index of the column to override the width of.\n * @param overrideValue - (Optional) The width value to use for the overridden column.\n * @returns An object containing the colgroup element, the total width of the table, and the minimum width of the table.\n */\nexport function createColGroup(\n  node: ProseMirrorNode,\n  cellMinWidth: number,\n): ColGroup\nexport function createColGroup(\n  node: ProseMirrorNode,\n  cellMinWidth: number,\n  overrideCol: number,\n  overrideValue: number,\n): ColGroup\nexport function createColGroup(\n  node: ProseMirrorNode,\n  cellMinWidth: number,\n  overrideCol?: number,\n  overrideValue?: number,\n): ColGroup {\n  let totalWidth = 0\n  let fixedWidth = true\n  const cols: DOMOutputSpec[] = []\n  const row = node.firstChild\n\n  if (!row) {\n    return {}\n  }\n\n  for (let i = 0, col = 0; i < row.childCount; i += 1) {\n    const { colspan, colwidth } = row.child(i).attrs\n\n    for (let j = 0; j < colspan; j += 1, col += 1) {\n      const hasWidth = overrideCol === col ? overrideValue : colwidth && colwidth[j] as number | undefined\n\n      totalWidth += hasWidth || cellMinWidth\n\n      if (!hasWidth) {\n        fixedWidth = false\n      }\n\n      const [property, value] = getColStyleDeclaration(cellMinWidth, hasWidth)\n\n      cols.push([\n        'col',\n        { style: `${property}: ${value}` },\n      ])\n    }\n  }\n\n  const tableWidth = fixedWidth ? `${totalWidth}px` : ''\n  const tableMinWidth = fixedWidth ? '' : `${totalWidth}px`\n\n  const colgroup: DOMOutputSpec = ['colgroup', {}, ...cols]\n\n  return { colgroup, tableWidth, tableMinWidth }\n}\n", "import { Fragment, Node as ProsemirrorNode, NodeType } from '@tiptap/pm/model'\n\nexport function createCell(\n  cellType: NodeType,\n  cellContent?: Fragment | ProsemirrorNode | Array<ProsemirrorNode>,\n): ProsemirrorNode | null | undefined {\n  if (cellContent) {\n    return cellType.createChecked(null, cellContent)\n  }\n\n  return cellType.createAndFill()\n}\n", "import { NodeType, Schema } from '@tiptap/pm/model'\n\nexport function getTableNodeTypes(schema: Schema): { [key: string]: NodeType } {\n  if (schema.cached.tableNodeTypes) {\n    return schema.cached.tableNodeTypes\n  }\n\n  const roles: { [key: string]: NodeType } = {}\n\n  Object.keys(schema.nodes).forEach(type => {\n    const nodeType = schema.nodes[type]\n\n    if (nodeType.spec.tableRole) {\n      roles[nodeType.spec.tableRole] = nodeType\n    }\n  })\n\n  schema.cached.tableNodeTypes = roles\n\n  return roles\n}\n", "import { Fragment, Node as ProsemirrorN<PERSON>, Schema } from '@tiptap/pm/model'\n\nimport { createCell } from './createCell.js'\nimport { getTableNodeTypes } from './getTableNodeTypes.js'\n\nexport function createTable(\n  schema: Schema,\n  rowsCount: number,\n  colsCount: number,\n  withHeaderRow: boolean,\n  cellContent?: Fragment | ProsemirrorNode | Array<ProsemirrorNode>,\n): ProsemirrorNode {\n  const types = getTableNodeTypes(schema)\n  const headerCells: ProsemirrorNode[] = []\n  const cells: ProsemirrorNode[] = []\n\n  for (let index = 0; index < colsCount; index += 1) {\n    const cell = createCell(types.cell, cellContent)\n\n    if (cell) {\n      cells.push(cell)\n    }\n\n    if (withHeaderRow) {\n      const headerCell = createCell(types.header_cell, cellContent)\n\n      if (headerCell) {\n        headerCells.push(headerCell)\n      }\n    }\n  }\n\n  const rows: ProsemirrorNode[] = []\n\n  for (let index = 0; index < rowsCount; index += 1) {\n    rows.push(types.row.createChecked(null, withHeaderRow && index === 0 ? headerCells : cells))\n  }\n\n  return types.table.createChecked(null, rows)\n}\n", "import { CellSelection } from '@tiptap/pm/tables'\n\nexport function isCellSelection(value: unknown): value is CellSelection {\n  return value instanceof CellSelection\n}\n", "import { findParentNodeClosestToPos, KeyboardShortcutCommand } from '@tiptap/core'\n\nimport { isCellSelection } from './isCellSelection.js'\n\nexport const deleteTableWhenAllCellsSelected: KeyboardShortcutCommand = ({ editor }) => {\n  const { selection } = editor.state\n\n  if (!isCellSelection(selection)) {\n    return false\n  }\n\n  let cellCount = 0\n  const table = findParentNodeClosestToPos(selection.ranges[0].$from, node => {\n    return node.type.name === 'table'\n  })\n\n  table?.node.descendants(node => {\n    if (node.type.name === 'table') {\n      return false\n    }\n\n    if (['tableCell', 'tableHeader'].includes(node.type.name)) {\n      cellCount += 1\n    }\n  })\n\n  const allCellsSelected = cellCount === selection.ranges.length\n\n  if (!allCellsSelected) {\n    return false\n  }\n\n  editor.commands.deleteTable()\n\n  return true\n}\n", "import {\n  callOr<PERSON><PERSON><PERSON>, getExtens<PERSON><PERSON>ield, merge<PERSON>tt<PERSON>but<PERSON>, Node, ParentConfig,\n} from '@tiptap/core'\nimport { DOMOutputSpec, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\nimport {\n  addColumnAfter,\n  addColumnBefore,\n  addRowAfter,\n  addRowBefore,\n  CellSelection,\n  columnResizing,\n  deleteColumn,\n  deleteRow,\n  deleteTable,\n  fixTables,\n  goToNextCell,\n  mergeCells,\n  setCellAttr,\n  splitCell,\n  tableEditing,\n  toggleHeader,\n  toggleHeaderCell,\n} from '@tiptap/pm/tables'\nimport { EditorView, NodeView } from '@tiptap/pm/view'\n\nimport { TableView } from './TableView.js'\nimport { createColGroup } from './utilities/createColGroup.js'\nimport { createTable } from './utilities/createTable.js'\nimport { deleteTableWhenAllCellsSelected } from './utilities/deleteTableWhenAllCellsSelected.js'\n\nexport interface TableOptions {\n  /**\n   * HTML attributes for the table element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * Enables the resizing of tables.\n   * @default false\n   * @example true\n   */\n  resizable: boolean\n\n  /**\n   * The width of the resize handle.\n   * @default 5\n   * @example 10\n   */\n  handleWidth: number\n\n  /**\n   * The minimum width of a cell.\n   * @default 25\n   * @example 50\n   */\n  cellMinWidth: number\n\n  /**\n   * The node view to render the table.\n   * @default TableView\n   */\n  View: (new (node: ProseMirrorNode, cellMinWidth: number, view: EditorView) => NodeView) | null\n\n  /**\n   * Enables the resizing of the last column.\n   * @default true\n   * @example false\n   */\n  lastColumnResizable: boolean\n\n  /**\n   * Allow table node selection.\n   * @default false\n   * @example true\n   */\n  allowTableNodeSelection: boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    table: {\n      /**\n       * Insert a table\n       * @param options The table attributes\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.insertTable({ rows: 3, cols: 3, withHeaderRow: true })\n       */\n      insertTable: (options?: {\n        rows?: number\n        cols?: number\n        withHeaderRow?: boolean\n      }) => ReturnType\n\n      /**\n       * Add a column before the current column\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.addColumnBefore()\n       */\n      addColumnBefore: () => ReturnType\n\n      /**\n       * Add a column after the current column\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.addColumnAfter()\n       */\n      addColumnAfter: () => ReturnType\n\n      /**\n       * Delete the current column\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.deleteColumn()\n       */\n      deleteColumn: () => ReturnType\n\n      /**\n       * Add a row before the current row\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.addRowBefore()\n       */\n      addRowBefore: () => ReturnType\n\n      /**\n       * Add a row after the current row\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.addRowAfter()\n       */\n      addRowAfter: () => ReturnType\n\n      /**\n       * Delete the current row\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.deleteRow()\n       */\n      deleteRow: () => ReturnType\n\n      /**\n       * Delete the current table\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.deleteTable()\n       */\n      deleteTable: () => ReturnType\n\n      /**\n       * Merge the currently selected cells\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.mergeCells()\n       */\n      mergeCells: () => ReturnType\n\n      /**\n       * Split the currently selected cell\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.splitCell()\n       */\n      splitCell: () => ReturnType\n\n      /**\n       * Toggle the header column\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.toggleHeaderColumn()\n       */\n      toggleHeaderColumn: () => ReturnType\n\n      /**\n       * Toggle the header row\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.toggleHeaderRow()\n       */\n      toggleHeaderRow: () => ReturnType\n\n      /**\n       * Toggle the header cell\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.toggleHeaderCell()\n       */\n      toggleHeaderCell: () => ReturnType\n\n      /**\n       * Merge or split the currently selected cells\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.mergeOrSplit()\n       */\n      mergeOrSplit: () => ReturnType\n\n      /**\n       * Set a cell attribute\n       * @param name The attribute name\n       * @param value The attribute value\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.setCellAttribute('align', 'right')\n       */\n      setCellAttribute: (name: string, value: any) => ReturnType\n\n      /**\n       * Moves the selection to the next cell\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.goToNextCell()\n       */\n      goToNextCell: () => ReturnType\n\n      /**\n       * Moves the selection to the previous cell\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.goToPreviousCell()\n       */\n      goToPreviousCell: () => ReturnType\n\n      /**\n       * Try to fix the table structure if necessary\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.fixTables()\n       */\n      fixTables: () => ReturnType\n\n      /**\n       * Set a cell selection inside the current table\n       * @param position The cell position\n       * @returns True if the command was successful, otherwise false\n       * @example editor.commands.setCellSelection({ anchorCell: 1, headCell: 2 })\n       */\n      setCellSelection: (position: { anchorCell: number; headCell?: number }) => ReturnType\n    }\n  }\n\n  interface NodeConfig<Options, Storage> {\n    /**\n     * A string or function to determine the role of the table.\n     * @default 'table'\n     * @example () => 'table'\n     */\n    tableRole?:\n      | string\n      | ((this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options>>['tableRole']\n    }) => string)\n  }\n}\n\n/**\n * This extension allows you to create tables.\n * @see https://www.tiptap.dev/api/nodes/table\n */\nexport const Table = Node.create<TableOptions>({\n  name: 'table',\n\n  // @ts-ignore\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      resizable: false,\n      handleWidth: 5,\n      cellMinWidth: 25,\n      // TODO: fix\n      View: TableView,\n      lastColumnResizable: true,\n      allowTableNodeSelection: false,\n    }\n  },\n\n  content: 'tableRow+',\n\n  tableRole: 'table',\n\n  isolating: true,\n\n  group: 'block',\n\n  parseHTML() {\n    return [{ tag: 'table' }]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    const { colgroup, tableWidth, tableMinWidth } = createColGroup(\n      node,\n      this.options.cellMinWidth,\n    )\n\n    const table: DOMOutputSpec = [\n      'table',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {\n        style: tableWidth\n          ? `width: ${tableWidth}`\n          : `min-width: ${tableMinWidth}`,\n      }),\n      colgroup,\n      ['tbody', 0],\n    ]\n\n    return table\n  },\n\n  addCommands() {\n    return {\n      insertTable:\n        ({ rows = 3, cols = 3, withHeaderRow = true } = {}) => ({ tr, dispatch, editor }) => {\n          const node = createTable(editor.schema, rows, cols, withHeaderRow)\n\n          if (dispatch) {\n            const offset = tr.selection.from + 1\n\n            tr.replaceSelectionWith(node)\n              .scrollIntoView()\n              .setSelection(TextSelection.near(tr.doc.resolve(offset)))\n          }\n\n          return true\n        },\n      addColumnBefore:\n        () => ({ state, dispatch }) => {\n          return addColumnBefore(state, dispatch)\n        },\n      addColumnAfter:\n        () => ({ state, dispatch }) => {\n          return addColumnAfter(state, dispatch)\n        },\n      deleteColumn:\n        () => ({ state, dispatch }) => {\n          return deleteColumn(state, dispatch)\n        },\n      addRowBefore:\n        () => ({ state, dispatch }) => {\n          return addRowBefore(state, dispatch)\n        },\n      addRowAfter:\n        () => ({ state, dispatch }) => {\n          return addRowAfter(state, dispatch)\n        },\n      deleteRow:\n        () => ({ state, dispatch }) => {\n          return deleteRow(state, dispatch)\n        },\n      deleteTable:\n        () => ({ state, dispatch }) => {\n          return deleteTable(state, dispatch)\n        },\n      mergeCells:\n        () => ({ state, dispatch }) => {\n          return mergeCells(state, dispatch)\n        },\n      splitCell:\n        () => ({ state, dispatch }) => {\n          return splitCell(state, dispatch)\n        },\n      toggleHeaderColumn:\n        () => ({ state, dispatch }) => {\n          return toggleHeader('column')(state, dispatch)\n        },\n      toggleHeaderRow:\n        () => ({ state, dispatch }) => {\n          return toggleHeader('row')(state, dispatch)\n        },\n      toggleHeaderCell:\n        () => ({ state, dispatch }) => {\n          return toggleHeaderCell(state, dispatch)\n        },\n      mergeOrSplit:\n        () => ({ state, dispatch }) => {\n          if (mergeCells(state, dispatch)) {\n            return true\n          }\n\n          return splitCell(state, dispatch)\n        },\n      setCellAttribute:\n        (name, value) => ({ state, dispatch }) => {\n          return setCellAttr(name, value)(state, dispatch)\n        },\n      goToNextCell:\n        () => ({ state, dispatch }) => {\n          return goToNextCell(1)(state, dispatch)\n        },\n      goToPreviousCell:\n        () => ({ state, dispatch }) => {\n          return goToNextCell(-1)(state, dispatch)\n        },\n      fixTables:\n        () => ({ state, dispatch }) => {\n          if (dispatch) {\n            fixTables(state)\n          }\n\n          return true\n        },\n      setCellSelection:\n        position => ({ tr, dispatch }) => {\n          if (dispatch) {\n            const selection = CellSelection.create(tr.doc, position.anchorCell, position.headCell)\n\n            // @ts-ignore\n            tr.setSelection(selection)\n          }\n\n          return true\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Tab: () => {\n        if (this.editor.commands.goToNextCell()) {\n          return true\n        }\n\n        if (!this.editor.can().addRowAfter()) {\n          return false\n        }\n\n        return this.editor.chain().addRowAfter().goToNextCell().run()\n      },\n      'Shift-Tab': () => this.editor.commands.goToPreviousCell(),\n      Backspace: deleteTableWhenAllCellsSelected,\n      'Mod-Backspace': deleteTableWhenAllCellsSelected,\n      Delete: deleteTableWhenAllCellsSelected,\n      'Mod-Delete': deleteTableWhenAllCellsSelected,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    const isResizable = this.options.resizable && this.editor.isEditable\n\n    return [\n      ...(isResizable\n        ? [\n          columnResizing({\n            handleWidth: this.options.handleWidth,\n            cellMinWidth: this.options.cellMinWidth,\n            defaultCellMinWidth: this.options.cellMinWidth,\n            View: this.options.View,\n            lastColumnResizable: this.options.lastColumnResizable,\n          }),\n        ]\n        : []),\n      tableEditing({\n        allowTableNodeSelection: this.options.allowTableNodeSelection,\n      }),\n    ]\n  },\n\n  extendNodeSchema(extension) {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    return {\n      tableRole: callOrReturn(getExtensionField(extension, 'tableRole', context)),\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAcA,IAAI;AACJ,IAAI;AACJ,IAAI,OAAO,WAAW,aAAa;AACjC,MAAI,QAAwB,oBAAI,QAAQ;AACxC,kBAAgB,CAAC,QAAQ,MAAM,IAAI,GAAG;AACtC,eAAa,CAAC,KAAK,UAAU;AAC3B,UAAM,IAAI,KAAK,KAAK;AACpB,WAAO;AAAA,EACT;AACF,OAAO;AACL,QAAM,QAAQ,CAAC;AACf,QAAM,YAAY;AAClB,MAAI,WAAW;AACf,kBAAgB,CAAC,QAAQ;AACvB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,MAAM,CAAC,KAAK,IAAK,QAAO,MAAM,IAAI,CAAC;AAAA,EAC3C;AACA,eAAa,CAAC,KAAK,UAAU;AAC3B,QAAI,YAAY,UAAW,YAAW;AACtC,UAAM,UAAU,IAAI;AACpB,WAAO,MAAM,UAAU,IAAI;AAAA,EAC7B;AACF;AACA,IAAI,WAAW,MAAM;AAAA,EACnB,YAAY,OAAO,QAAQ,KAAK,UAAU;AACxC,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,SAAS,KAAK;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,IAAI,QAAQ,KAAK;AACxC,YAAM,SAAS,KAAK,IAAI,CAAC;AACzB,UAAI,UAAU,IAAK;AACnB,YAAM,OAAO,IAAI,KAAK;AACtB,YAAM,MAAM,IAAI,KAAK,QAAQ;AAC7B,UAAI,QAAQ,OAAO;AACnB,UAAI,SAAS,MAAM;AACnB,eAAS,IAAI,GAAG,QAAQ,KAAK,SAAS,KAAK,IAAI,IAAI,CAAC,KAAK,QAAQ,KAAK;AACpE;AAAA,MACF;AACA,eAAS,IAAI,GAAG,SAAS,KAAK,UAAU,KAAK,IAAI,IAAI,KAAK,QAAQ,CAAC,KAAK,QAAQ,KAAK;AACnF;AAAA,MACF;AACA,aAAO,EAAE,MAAM,KAAK,OAAO,OAAO;AAAA,IACpC;AACA,UAAM,IAAI,WAAW,uBAAuB,GAAG,QAAQ;AAAA,EACzD;AAAA;AAAA,EAEA,SAAS,KAAK;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,IAAI,QAAQ,KAAK;AACxC,UAAI,KAAK,IAAI,CAAC,KAAK,KAAK;AACtB,eAAO,IAAI,KAAK;AAAA,MAClB;AAAA,IACF;AACA,UAAM,IAAI,WAAW,uBAAuB,GAAG,QAAQ;AAAA,EACzD;AAAA;AAAA;AAAA,EAGA,SAAS,KAAK,MAAM,KAAK;AACvB,UAAM,EAAE,MAAM,OAAO,KAAK,OAAO,IAAI,KAAK,SAAS,GAAG;AACtD,QAAI,QAAQ,SAAS;AACnB,UAAI,MAAM,IAAI,QAAQ,IAAI,SAAS,KAAK,MAAO,QAAO;AACtD,aAAO,KAAK,IAAI,MAAM,KAAK,SAAS,MAAM,IAAI,OAAO,IAAI,MAAM;AAAA,IACjE,OAAO;AACL,UAAI,MAAM,IAAI,OAAO,IAAI,UAAU,KAAK,OAAQ,QAAO;AACvD,aAAO,KAAK,IAAI,OAAO,KAAK,SAAS,MAAM,IAAI,MAAM,IAAI,OAAO;AAAA,IAClE;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,GAAG,GAAG;AAChB,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,IACV,IAAI,KAAK,SAAS,CAAC;AACnB,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,IACV,IAAI,KAAK,SAAS,CAAC;AACnB,WAAO;AAAA,MACL,MAAM,KAAK,IAAI,OAAO,KAAK;AAAA,MAC3B,KAAK,KAAK,IAAI,MAAM,IAAI;AAAA,MACxB,OAAO,KAAK,IAAI,QAAQ,MAAM;AAAA,MAC9B,QAAQ,KAAK,IAAI,SAAS,OAAO;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,YAAY,MAAM;AAChB,UAAM,SAAS,CAAC;AAChB,UAAM,OAAO,CAAC;AACd,aAAS,MAAM,KAAK,KAAK,MAAM,KAAK,QAAQ,OAAO;AACjD,eAAS,MAAM,KAAK,MAAM,MAAM,KAAK,OAAO,OAAO;AACjD,cAAM,QAAQ,MAAM,KAAK,QAAQ;AACjC,cAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,YAAI,KAAK,GAAG,EAAG;AACf,aAAK,GAAG,IAAI;AACZ,YAAI,OAAO,KAAK,QAAQ,OAAO,KAAK,IAAI,QAAQ,CAAC,KAAK,OAAO,OAAO,KAAK,OAAO,OAAO,KAAK,IAAI,QAAQ,KAAK,KAAK,KAAK,KAAK;AAC1H;AAAA,QACF;AACA,eAAO,KAAK,GAAG;AAAA,MACjB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAGA,WAAW,KAAK,KAAK,OAAO;AAC1B,aAAS,IAAI,GAAG,WAAW,KAAK,KAAK;AACnC,YAAM,SAAS,WAAW,MAAM,MAAM,CAAC,EAAE;AACzC,UAAI,KAAK,KAAK;AACZ,YAAI,QAAQ,MAAM,MAAM,KAAK;AAC7B,cAAM,eAAe,MAAM,KAAK,KAAK;AACrC,eAAO,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,SAAU;AAC1D,eAAO,SAAS,cAAc,SAAS,IAAI,KAAK,IAAI,KAAK;AAAA,MAC3D;AACA,iBAAW;AAAA,IACb;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,IAAI,OAAO;AAChB,WAAO,cAAc,KAAK,KAAK,WAAW,OAAO,WAAW,KAAK,CAAC;AAAA,EACpE;AACF;AACA,SAAS,WAAW,OAAO;AACzB,MAAI,MAAM,KAAK,KAAK,aAAa;AAC/B,UAAM,IAAI,WAAW,uBAAuB,MAAM,KAAK,IAAI;AAC7D,QAAM,QAAQ,UAAU,KAAK,GAAG,SAAS,MAAM;AAC/C,QAAM,MAAM,CAAC;AACb,MAAI,SAAS;AACb,MAAI,WAAW;AACf,QAAM,YAAY,CAAC;AACnB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,IAAK,KAAI,CAAC,IAAI;AACzD,WAAS,MAAM,GAAG,MAAM,GAAG,MAAM,QAAQ,OAAO;AAC9C,UAAM,UAAU,MAAM,MAAM,GAAG;AAC/B;AACA,aAAS,IAAI,KAAK,KAAK;AACrB,aAAO,SAAS,IAAI,UAAU,IAAI,MAAM,KAAK,EAAG;AAChD,UAAI,KAAK,QAAQ,WAAY;AAC7B,YAAM,WAAW,QAAQ,MAAM,CAAC;AAChC,YAAM,EAAE,SAAS,SAAS,SAAS,IAAI,SAAS;AAChD,eAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,YAAI,IAAI,OAAO,QAAQ;AACrB,WAAC,aAAa,WAAW,CAAC,IAAI,KAAK;AAAA,YACjC,MAAM;AAAA,YACN;AAAA,YACA,GAAG,UAAU;AAAA,UACf,CAAC;AACD;AAAA,QACF;AACA,cAAM,QAAQ,SAAS,IAAI;AAC3B,iBAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,cAAI,IAAI,QAAQ,CAAC,KAAK,EAAG,KAAI,QAAQ,CAAC,IAAI;AAAA;AAExC,aAAC,aAAa,WAAW,CAAC,IAAI,KAAK;AAAA,cACjC,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA,GAAG,UAAU;AAAA,YACf,CAAC;AACH,gBAAM,OAAO,YAAY,SAAS,CAAC;AACnC,cAAI,MAAM;AACR,kBAAM,cAAc,QAAQ,KAAK,QAAQ,GAAG,OAAO,UAAU,UAAU;AACvE,gBAAI,QAAQ,QAAQ,QAAQ,QAAQ,UAAU,aAAa,CAAC,KAAK,GAAG;AAClE,wBAAU,UAAU,IAAI;AACxB,wBAAU,aAAa,CAAC,IAAI;AAAA,YAC9B,WAAW,QAAQ,MAAM;AACvB,wBAAU,aAAa,CAAC;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,gBAAU;AACV,aAAO,SAAS;AAAA,IAClB;AACA,UAAM,eAAe,MAAM,KAAK;AAChC,QAAI,UAAU;AACd,WAAO,SAAS,YAAa,KAAI,IAAI,QAAQ,KAAK,EAAG;AACrD,QAAI;AACF,OAAC,aAAa,WAAW,CAAC,IAAI,KAAK,EAAE,MAAM,WAAW,KAAK,GAAG,QAAQ,CAAC;AACzE;AAAA,EACF;AACA,MAAI,UAAU,KAAK,WAAW;AAC5B,KAAC,aAAa,WAAW,CAAC,IAAI,KAAK,EAAE,MAAM,aAAa,CAAC;AAC3D,QAAM,WAAW,IAAI,SAAS,OAAO,QAAQ,KAAK,QAAQ;AAC1D,MAAI,YAAY;AAChB,WAAS,IAAI,GAAG,CAAC,aAAa,IAAI,UAAU,QAAQ,KAAK;AACvD,QAAI,UAAU,CAAC,KAAK,QAAQ,UAAU,IAAI,CAAC,IAAI,OAAQ,aAAY;AACrE,MAAI,UAAW,kBAAiB,UAAU,WAAW,KAAK;AAC1D,SAAO;AACT;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,QAAQ;AACZ,MAAI,aAAa;AACjB,WAAS,MAAM,GAAG,MAAM,MAAM,YAAY,OAAO;AAC/C,UAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,QAAI,WAAW;AACf,QAAI;AACF,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAM,UAAU,MAAM,MAAM,CAAC;AAC7B,iBAAS,IAAI,GAAG,IAAI,QAAQ,YAAY,KAAK;AAC3C,gBAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,cAAI,IAAI,KAAK,MAAM,UAAU,IAAK,aAAY,KAAK,MAAM;AAAA,QAC3D;AAAA,MACF;AACF,aAAS,IAAI,GAAG,IAAI,QAAQ,YAAY,KAAK;AAC3C,YAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,kBAAY,KAAK,MAAM;AACvB,UAAI,KAAK,MAAM,UAAU,EAAG,cAAa;AAAA,IAC3C;AACA,QAAI,SAAS,GAAI,SAAQ;AAAA,aAChB,SAAS,SAAU,SAAQ,KAAK,IAAI,OAAO,QAAQ;AAAA,EAC9D;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,KAAK,WAAW,OAAO;AAC/C,MAAI,CAAC,IAAI,SAAU,KAAI,WAAW,CAAC;AACnC,QAAM,OAAO,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,IAAI,IAAI,QAAQ,KAAK;AACvC,UAAM,MAAM,IAAI,IAAI,CAAC;AACrB,QAAI,KAAK,GAAG,EAAG;AACf,SAAK,GAAG,IAAI;AACZ,UAAM,OAAO,MAAM,OAAO,GAAG;AAC7B,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,WAAW,uBAAuB,GAAG,QAAQ;AAAA,IACzD;AACA,QAAI,UAAU;AACd,UAAM,QAAQ,KAAK;AACnB,aAAS,IAAI,GAAG,IAAI,MAAM,SAAS,KAAK;AACtC,YAAM,OAAO,IAAI,KAAK,IAAI;AAC1B,YAAM,WAAW,UAAU,MAAM,CAAC;AAClC,UAAI,YAAY,SAAS,CAAC,MAAM,YAAY,MAAM,SAAS,CAAC,KAAK;AAC/D,SAAC,YAAY,UAAU,cAAc,KAAK,IAAI,CAAC,IAAI;AAAA,IACvD;AACA,QAAI;AACF,UAAI,SAAS,QAAQ;AAAA,QACnB,MAAM;AAAA,QACN;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AAAA,EACL;AACF;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,MAAM,SAAU,QAAO,MAAM,SAAS,MAAM;AAChD,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,MAAM,SAAS,IAAK,QAAO,KAAK,CAAC;AACrD,SAAO;AACT;AA6GA,SAAS,eAAe,QAAQ;AAC9B,MAAI,SAAS,OAAO,OAAO;AAC3B,MAAI,CAAC,QAAQ;AACX,aAAS,OAAO,OAAO,iBAAiB,CAAC;AACzC,eAAW,QAAQ,OAAO,OAAO;AAC/B,YAAM,OAAO,OAAO,MAAM,IAAI,GAAG,OAAO,KAAK,KAAK;AAClD,UAAI,KAAM,QAAO,IAAI,IAAI;AAAA,IAC3B;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAI,kBAAkB,IAAI,UAAU,gBAAgB;AACpD,SAAS,WAAW,MAAM;AACxB,WAAS,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG;AAClC,QAAI,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,aAAa;AACtC,aAAO,KAAK,KAAK,CAAC,EAAE,QAAQ,KAAK,OAAO,IAAI,CAAC,CAAC;AAClD,SAAO;AACT;AACA,SAAS,aAAa,MAAM;AAC1B,WAAS,IAAI,KAAK,OAAO,IAAI,GAAG,KAAK;AACnC,UAAM,OAAO,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK;AACpC,QAAI,SAAS,UAAU,SAAS,cAAe,QAAO,KAAK,KAAK,CAAC;AAAA,EACnE;AACA,SAAO;AACT;AACA,SAAS,UAAU,OAAO;AACxB,QAAM,QAAQ,MAAM,UAAU;AAC9B,WAAS,IAAI,MAAM,OAAO,IAAI,GAAG;AAC/B,QAAI,MAAM,KAAK,CAAC,EAAE,KAAK,KAAK,aAAa,MAAO,QAAO;AACzD,SAAO;AACT;AACA,SAAS,cAAc,OAAO;AAC5B,QAAM,MAAM,MAAM;AAClB,MAAI,iBAAiB,OAAO,IAAI,aAAa;AAC3C,WAAO,IAAI,YAAY,MAAM,IAAI,UAAU,MAAM,IAAI,cAAc,IAAI;AAAA,EACzE,WAAW,UAAU,OAAO,IAAI,QAAQ,IAAI,KAAK,KAAK,KAAK,aAAa,QAAQ;AAC9E,WAAO,IAAI;AAAA,EACb;AACA,QAAM,QAAQ,WAAW,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK;AACzD,MAAI,OAAO;AACT,WAAO;AAAA,EACT;AACA,QAAM,IAAI,WAAW,iCAAiC,IAAI,IAAI,EAAE;AAClE;AACA,SAAS,SAAS,MAAM;AACtB,WAAS,QAAQ,KAAK,WAAW,MAAM,KAAK,KAAK,OAAO,QAAQ,MAAM,YAAY,OAAO;AACvF,UAAM,OAAO,MAAM,KAAK,KAAK;AAC7B,QAAI,QAAQ,UAAU,QAAQ,cAAe,QAAO,KAAK,IAAI,QAAQ,GAAG;AAAA,EAC1E;AACA,WAAS,SAAS,KAAK,YAAY,MAAM,KAAK,KAAK,QAAQ,SAAS,OAAO,WAAW,OAAO;AAC3F,UAAM,OAAO,OAAO,KAAK,KAAK;AAC9B,QAAI,QAAQ,UAAU,QAAQ;AAC5B,aAAO,KAAK,IAAI,QAAQ,MAAM,OAAO,QAAQ;AAAA,EACjD;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,KAAK,OAAO,KAAK,KAAK,aAAa,SAAS,CAAC,CAAC,KAAK;AAC5D;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,KAAK,KAAK,CAAC,EAAE,QAAQ,KAAK,MAAM,KAAK,UAAU,QAAQ;AAChE;AACA,SAAS,YAAY,QAAQ,QAAQ;AACnC,SAAO,OAAO,SAAS,OAAO,SAAS,OAAO,OAAO,OAAO,MAAM,EAAE,KAAK,OAAO,OAAO,OAAO,IAAI,EAAE;AACtG;AAOA,SAAS,SAAS,MAAM,MAAM,KAAK;AACjC,QAAM,QAAQ,KAAK,KAAK,EAAE;AAC1B,QAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,QAAM,aAAa,KAAK,MAAM,EAAE;AAChC,QAAM,QAAQ,IAAI,SAAS,KAAK,MAAM,YAAY,MAAM,GAAG;AAC3D,SAAO,SAAS,OAAO,OAAO,KAAK,KAAK,CAAC,EAAE,QAAQ,aAAa,KAAK;AACvE;AACA,SAAS,cAAc,OAAO,KAAK,IAAI,GAAG;AACxC,QAAM,SAAS,EAAE,GAAG,OAAO,SAAS,MAAM,UAAU,EAAE;AACtD,MAAI,OAAO,UAAU;AACnB,WAAO,WAAW,OAAO,SAAS,MAAM;AACxC,WAAO,SAAS,OAAO,KAAK,CAAC;AAC7B,QAAI,CAAC,OAAO,SAAS,KAAK,CAAC,MAAM,IAAI,CAAC,EAAG,QAAO,WAAW;AAAA,EAC7D;AACA,SAAO;AACT;AACA,SAAS,WAAW,OAAO,KAAK,IAAI,GAAG;AACrC,QAAM,SAAS,EAAE,GAAG,OAAO,SAAS,MAAM,UAAU,EAAE;AACtD,MAAI,OAAO,UAAU;AACnB,WAAO,WAAW,OAAO,SAAS,MAAM;AACxC,aAAS,IAAI,GAAG,IAAI,GAAG,IAAK,QAAO,SAAS,OAAO,KAAK,GAAG,CAAC;AAAA,EAC9D;AACA,SAAO;AACT;AACA,SAAS,eAAe,KAAK,OAAO,KAAK;AACvC,QAAM,aAAa,eAAe,MAAM,KAAK,MAAM,EAAE;AACrD,WAAS,MAAM,GAAG,MAAM,IAAI,QAAQ;AAClC,QAAI,MAAM,OAAO,IAAI,IAAI,MAAM,MAAM,IAAI,KAAK,CAAC,EAAE,QAAQ;AACvD,aAAO;AACX,SAAO;AACT;AAGA,IAAI,gBAAgB,MAAM,uBAAuB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzD,YAAY,aAAa,YAAY,aAAa;AAChD,UAAM,QAAQ,YAAY,KAAK,EAAE;AACjC,UAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,UAAM,aAAa,YAAY,MAAM,EAAE;AACvC,UAAM,OAAO,IAAI;AAAA,MACf,YAAY,MAAM;AAAA,MAClB,UAAU,MAAM;AAAA,IAClB;AACA,UAAM,MAAM,YAAY,KAAK,CAAC;AAC9B,UAAM,QAAQ,IAAI,YAAY,IAAI,EAAE,OAAO,CAAC,MAAM,KAAK,UAAU,MAAM,UAAU;AACjF,UAAM,QAAQ,UAAU,MAAM,UAAU;AACxC,UAAM,SAAS,MAAM,IAAI,CAAC,QAAQ;AAChC,YAAM,OAAO,MAAM,OAAO,GAAG;AAC7B,UAAI,CAAC,MAAM;AACT,cAAM,WAAW,uBAAuB,GAAG,QAAQ;AAAA,MACrD;AACA,YAAM,OAAO,aAAa,MAAM;AAChC,aAAO,IAAI;AAAA,QACT,IAAI,QAAQ,IAAI;AAAA,QAChB,IAAI,QAAQ,OAAO,KAAK,QAAQ,IAAI;AAAA,MACtC;AAAA,IACF,CAAC;AACD,UAAM,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE,KAAK,MAAM;AAC5C,SAAK,cAAc;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,IAAI,KAAK,SAAS;AAChB,UAAM,cAAc,IAAI,QAAQ,QAAQ,IAAI,KAAK,YAAY,GAAG,CAAC;AACjE,UAAM,YAAY,IAAI,QAAQ,QAAQ,IAAI,KAAK,UAAU,GAAG,CAAC;AAC7D,QAAI,aAAa,WAAW,KAAK,aAAa,SAAS,KAAK,YAAY,aAAa,SAAS,GAAG;AAC/F,YAAM,eAAe,KAAK,YAAY,KAAK,EAAE,KAAK,YAAY,KAAK,EAAE;AACrE,UAAI,gBAAgB,KAAK,eAAe;AACtC,eAAO,eAAe,aAAa,aAAa,SAAS;AAAA,eAClD,gBAAgB,KAAK,eAAe;AAC3C,eAAO,eAAe,aAAa,aAAa,SAAS;AAAA,UACtD,QAAO,IAAI,eAAe,aAAa,SAAS;AAAA,IACvD;AACA,WAAO,cAAc,QAAQ,aAAa,SAAS;AAAA,EACrD;AAAA;AAAA;AAAA,EAGA,UAAU;AACR,UAAM,QAAQ,KAAK,YAAY,KAAK,EAAE;AACtC,UAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,UAAM,aAAa,KAAK,YAAY,MAAM,EAAE;AAC5C,UAAM,OAAO,IAAI;AAAA,MACf,KAAK,YAAY,MAAM;AAAA,MACvB,KAAK,UAAU,MAAM;AAAA,IACvB;AACA,UAAM,OAAO,CAAC;AACd,UAAM,OAAO,CAAC;AACd,aAAS,MAAM,KAAK,KAAK,MAAM,KAAK,QAAQ,OAAO;AACjD,YAAM,aAAa,CAAC;AACpB,eAAS,QAAQ,MAAM,IAAI,QAAQ,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,OAAO,OAAO,SAAS;AAC/F,cAAM,MAAM,IAAI,IAAI,KAAK;AACzB,YAAI,KAAK,GAAG,EAAG;AACf,aAAK,GAAG,IAAI;AACZ,cAAM,WAAW,IAAI,SAAS,GAAG;AACjC,YAAI,OAAO,MAAM,OAAO,GAAG;AAC3B,YAAI,CAAC,MAAM;AACT,gBAAM,WAAW,uBAAuB,GAAG,QAAQ;AAAA,QACrD;AACA,cAAM,YAAY,KAAK,OAAO,SAAS;AACvC,cAAM,aAAa,SAAS,QAAQ,KAAK;AACzC,YAAI,YAAY,KAAK,aAAa,GAAG;AACnC,cAAI,QAAQ,KAAK;AACjB,cAAI,YAAY,GAAG;AACjB,oBAAQ,cAAc,OAAO,GAAG,SAAS;AAAA,UAC3C;AACA,cAAI,aAAa,GAAG;AAClB,oBAAQ;AAAA,cACN;AAAA,cACA,MAAM,UAAU;AAAA,cAChB;AAAA,YACF;AAAA,UACF;AACA,cAAI,SAAS,OAAO,KAAK,MAAM;AAC7B,mBAAO,KAAK,KAAK,cAAc,KAAK;AACpC,gBAAI,CAAC,MAAM;AACT,oBAAM;AAAA,gBACJ,oCAAoC,KAAK,UAAU,KAAK,CAAC;AAAA,cAC3D;AAAA,YACF;AAAA,UACF,OAAO;AACL,mBAAO,KAAK,KAAK,OAAO,OAAO,KAAK,OAAO;AAAA,UAC7C;AAAA,QACF;AACA,YAAI,SAAS,MAAM,KAAK,OAAO,SAAS,SAAS,KAAK,QAAQ;AAC5D,gBAAM,QAAQ;AAAA,YACZ,GAAG,KAAK;AAAA,YACR,SAAS,KAAK,IAAI,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK,GAAG;AAAA,UACnF;AACA,cAAI,SAAS,MAAM,KAAK,KAAK;AAC3B,mBAAO,KAAK,KAAK,cAAc,KAAK;AAAA,UACtC,OAAO;AACL,mBAAO,KAAK,KAAK,OAAO,OAAO,KAAK,OAAO;AAAA,UAC7C;AAAA,QACF;AACA,mBAAW,KAAK,IAAI;AAAA,MACtB;AACA,WAAK,KAAK,MAAM,MAAM,GAAG,EAAE,KAAK,SAAS,KAAK,UAAU,CAAC,CAAC;AAAA,IAC5D;AACA,UAAM,WAAW,KAAK,eAAe,KAAK,KAAK,eAAe,IAAI,QAAQ;AAC1E,WAAO,IAAI,MAAM,SAAS,KAAK,QAAQ,GAAG,GAAG,CAAC;AAAA,EAChD;AAAA,EACA,QAAQ,IAAI,UAAU,MAAM,OAAO;AACjC,UAAM,UAAU,GAAG,MAAM,QAAQ,SAAS,KAAK;AAC/C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,EAAE,OAAO,IAAI,IAAI,OAAO,CAAC,GAAG,UAAU,GAAG,QAAQ,MAAM,OAAO;AACpE,SAAG;AAAA,QACD,QAAQ,IAAI,MAAM,GAAG;AAAA,QACrB,QAAQ,IAAI,IAAI,GAAG;AAAA,QACnB,IAAI,MAAM,QAAQ;AAAA,MACpB;AAAA,IACF;AACA,UAAM,MAAM,UAAU;AAAA,MACpB,GAAG,IAAI,QAAQ,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,KAAK,EAAE,CAAC;AAAA,MACrD;AAAA,IACF;AACA,QAAI,IAAK,IAAG,aAAa,GAAG;AAAA,EAC9B;AAAA,EACA,YAAY,IAAI,MAAM;AACpB,SAAK,QAAQ,IAAI,IAAI,MAAM,SAAS,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC;AAAA,EACvD;AAAA,EACA,YAAY,GAAG;AACb,UAAM,QAAQ,KAAK,YAAY,KAAK,EAAE;AACtC,UAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,UAAM,aAAa,KAAK,YAAY,MAAM,EAAE;AAC5C,UAAM,QAAQ,IAAI;AAAA,MAChB,IAAI;AAAA,QACF,KAAK,YAAY,MAAM;AAAA,QACvB,KAAK,UAAU,MAAM;AAAA,MACvB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAE,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,aAAa,MAAM,CAAC,CAAC;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,iBAAiB;AACf,UAAM,YAAY,KAAK,YAAY,MAAM,EAAE;AAC3C,UAAM,UAAU,KAAK,UAAU,MAAM,EAAE;AACvC,QAAI,KAAK,IAAI,WAAW,OAAO,IAAI,EAAG,QAAO;AAC7C,UAAM,eAAe,YAAY,KAAK,YAAY,UAAU,MAAM;AAClE,UAAM,aAAa,UAAU,KAAK,UAAU,UAAU,MAAM;AAC5D,WAAO,KAAK,IAAI,cAAc,UAAU,KAAK,KAAK,UAAU,KAAK,EAAE,EAAE;AAAA,EACvE;AAAA;AAAA;AAAA,EAGA,OAAO,aAAa,aAAa,YAAY,aAAa;AACxD,UAAM,QAAQ,YAAY,KAAK,EAAE;AACjC,UAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,UAAM,aAAa,YAAY,MAAM,EAAE;AACvC,UAAM,aAAa,IAAI,SAAS,YAAY,MAAM,UAAU;AAC5D,UAAM,WAAW,IAAI,SAAS,UAAU,MAAM,UAAU;AACxD,UAAM,MAAM,YAAY,KAAK,CAAC;AAC9B,QAAI,WAAW,OAAO,SAAS,KAAK;AAClC,UAAI,WAAW,MAAM;AACnB,sBAAc,IAAI,QAAQ,aAAa,IAAI,IAAI,WAAW,IAAI,CAAC;AACjE,UAAI,SAAS,SAAS,IAAI;AACxB,oBAAY,IAAI;AAAA,UACd,aAAa,IAAI,IAAI,IAAI,SAAS,IAAI,SAAS,KAAK,SAAS,QAAQ,CAAC;AAAA,QACxE;AAAA,IACJ,OAAO;AACL,UAAI,SAAS,MAAM;AACjB,oBAAY,IAAI,QAAQ,aAAa,IAAI,IAAI,SAAS,IAAI,CAAC;AAC7D,UAAI,WAAW,SAAS,IAAI;AAC1B,sBAAc,IAAI;AAAA,UAChB,aAAa,IAAI,IAAI,IAAI,SAAS,IAAI,SAAS,KAAK,WAAW,QAAQ,CAAC;AAAA,QAC1E;AAAA,IACJ;AACA,WAAO,IAAI,eAAe,aAAa,SAAS;AAAA,EAClD;AAAA;AAAA;AAAA,EAGA,iBAAiB;AACf,UAAM,QAAQ,KAAK,YAAY,KAAK,EAAE;AACtC,UAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,UAAM,aAAa,KAAK,YAAY,MAAM,EAAE;AAC5C,UAAM,aAAa,IAAI,SAAS,KAAK,YAAY,MAAM,UAAU;AACjE,UAAM,WAAW,IAAI,SAAS,KAAK,UAAU,MAAM,UAAU;AAC7D,QAAI,KAAK,IAAI,YAAY,QAAQ,IAAI,EAAG,QAAO;AAC/C,UAAM,cAAc,aAAa,KAAK,YAAY,UAAU,MAAM;AAClE,UAAM,YAAY,WAAW,KAAK,UAAU,UAAU,MAAM;AAC5D,WAAO,KAAK,IAAI,aAAa,SAAS,KAAK,IAAI;AAAA,EACjD;AAAA,EACA,GAAG,OAAO;AACR,WAAO,iBAAiB,kBAAkB,MAAM,YAAY,OAAO,KAAK,YAAY,OAAO,MAAM,UAAU,OAAO,KAAK,UAAU;AAAA,EACnI;AAAA;AAAA;AAAA,EAGA,OAAO,aAAa,aAAa,YAAY,aAAa;AACxD,UAAM,QAAQ,YAAY,KAAK,EAAE;AACjC,UAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,UAAM,aAAa,YAAY,MAAM,EAAE;AACvC,UAAM,aAAa,IAAI,SAAS,YAAY,MAAM,UAAU;AAC5D,UAAM,WAAW,IAAI,SAAS,UAAU,MAAM,UAAU;AACxD,UAAM,MAAM,YAAY,KAAK,CAAC;AAC9B,QAAI,WAAW,QAAQ,SAAS,MAAM;AACpC,UAAI,WAAW,OAAO;AACpB,sBAAc,IAAI;AAAA,UAChB,aAAa,IAAI,IAAI,WAAW,MAAM,IAAI,KAAK;AAAA,QACjD;AACF,UAAI,SAAS,QAAQ,IAAI;AACvB,oBAAY,IAAI;AAAA,UACd,aAAa,IAAI,IAAI,IAAI,SAAS,SAAS,MAAM,KAAK,CAAC;AAAA,QACzD;AAAA,IACJ,OAAO;AACL,UAAI,SAAS,OAAO;AAClB,oBAAY,IAAI,QAAQ,aAAa,IAAI,IAAI,SAAS,MAAM,IAAI,KAAK,CAAC;AACxE,UAAI,WAAW,QAAQ,IAAI;AACzB,sBAAc,IAAI;AAAA,UAChB,aAAa,IAAI,IAAI,IAAI,SAAS,WAAW,MAAM,KAAK,CAAC;AAAA,QAC3D;AAAA,IACJ;AACA,WAAO,IAAI,eAAe,aAAa,SAAS;AAAA,EAClD;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,MAAM;AAAA,MACN,QAAQ,KAAK,YAAY;AAAA,MACzB,MAAM,KAAK,UAAU;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO,SAAS,KAAK,MAAM;AACzB,WAAO,IAAI,eAAe,IAAI,QAAQ,KAAK,MAAM,GAAG,IAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,EAC5E;AAAA,EACA,OAAO,OAAO,KAAK,YAAY,WAAW,YAAY;AACpD,WAAO,IAAI,eAAe,IAAI,QAAQ,UAAU,GAAG,IAAI,QAAQ,QAAQ,CAAC;AAAA,EAC1E;AAAA,EACA,cAAc;AACZ,WAAO,IAAI,aAAa,KAAK,YAAY,KAAK,KAAK,UAAU,GAAG;AAAA,EAClE;AACF;AACA,cAAc,UAAU,UAAU;AAClC,UAAU,OAAO,QAAQ,aAAa;AACtC,IAAI,eAAe,MAAM,cAAc;AAAA,EACrC,YAAY,QAAQ,MAAM;AACxB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AAAA,EACA,IAAI,SAAS;AACX,WAAO,IAAI,cAAc,QAAQ,IAAI,KAAK,MAAM,GAAG,QAAQ,IAAI,KAAK,IAAI,CAAC;AAAA,EAC3E;AAAA,EACA,QAAQ,KAAK;AACX,UAAM,cAAc,IAAI,QAAQ,KAAK,MAAM,GAAG,YAAY,IAAI,QAAQ,KAAK,IAAI;AAC/E,QAAI,YAAY,OAAO,KAAK,KAAK,aAAa,SAAS,UAAU,OAAO,KAAK,KAAK,aAAa,SAAS,YAAY,MAAM,IAAI,YAAY,OAAO,cAAc,UAAU,MAAM,IAAI,UAAU,OAAO,cAAc,YAAY,aAAa,SAAS;AAClP,aAAO,IAAI,cAAc,aAAa,SAAS;AAAA,QAC5C,QAAO,UAAU,KAAK,WAAW,CAAC;AAAA,EACzC;AACF;AACA,SAAS,kBAAkB,OAAO;AAChC,MAAI,EAAE,MAAM,qBAAqB,eAAgB,QAAO;AACxD,QAAM,QAAQ,CAAC;AACf,QAAM,UAAU,YAAY,CAAC,MAAM,QAAQ;AACzC,UAAM;AAAA,MACJ,WAAW,KAAK,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,eAAe,CAAC;AAAA,IACrE;AAAA,EACF,CAAC;AACD,SAAO,cAAc,OAAO,MAAM,KAAK,KAAK;AAC9C;AACA,SAAS,wBAAwB,EAAE,OAAO,IAAI,GAAG;AAC/C,MAAI,MAAM,OAAO,IAAI,OAAO,MAAM,MAAM,IAAI,MAAM,EAAG,QAAO;AAC5D,MAAI,YAAY,MAAM;AACtB,MAAI,WAAW,IAAI;AACnB,MAAI,QAAQ,MAAM;AAClB,SAAO,SAAS,GAAG,SAAS;AAC1B,QAAI,MAAM,MAAM,QAAQ,CAAC,IAAI,MAAM,IAAI,KAAK,EAAG;AACjD,WAAS,IAAI,IAAI,OAAO,KAAK,GAAG,KAAK;AACnC,QAAI,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,EAAG;AACxC,SAAO,aAAa,YAAY,YAAY,KAAK,MAAM,KAAK,KAAK,EAAE,KAAK,KAAK,SAAS;AACxF;AACA,SAAS,2BAA2B,EAAE,OAAO,IAAI,GAAG;AAClD,MAAI;AACJ,MAAI;AACJ,WAAS,IAAI,MAAM,OAAO,IAAI,GAAG,KAAK;AACpC,UAAM,OAAO,MAAM,KAAK,CAAC;AACzB,QAAI,KAAK,KAAK,KAAK,cAAc,UAAU,KAAK,KAAK,KAAK,cAAc,eAAe;AACrF,6BAAuB;AACvB;AAAA,IACF;AAAA,EACF;AACA,WAAS,IAAI,IAAI,OAAO,IAAI,GAAG,KAAK;AAClC,UAAM,OAAO,IAAI,KAAK,CAAC;AACvB,QAAI,KAAK,KAAK,KAAK,cAAc,UAAU,KAAK,KAAK,KAAK,cAAc,eAAe;AACrF,2BAAqB;AACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO,yBAAyB,sBAAsB,IAAI,iBAAiB;AAC7E;AACA,SAAS,mBAAmB,OAAO,IAAI,yBAAyB;AAC9D,QAAM,OAAO,MAAM,OAAO;AAC1B,QAAM,OAAO,MAAM,OAAO;AAC1B,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe,kBAAmB,OAAO,IAAI,KAAK,KAAK,KAAK,YAAY;AAC1E,QAAI,QAAQ,UAAU,QAAQ,eAAe;AAC3C,kBAAY,cAAc,OAAO,KAAK,IAAI,IAAI;AAAA,IAChD,WAAW,QAAQ,OAAO;AACxB,YAAM,QAAQ,IAAI,QAAQ,IAAI,OAAO,CAAC;AACtC,kBAAY,cAAc,aAAa,OAAO,KAAK;AAAA,IACrD,WAAW,CAAC,yBAAyB;AACnC,YAAM,MAAM,SAAS,IAAI,IAAI,IAAI;AACjC,YAAM,QAAQ,IAAI,OAAO;AACzB,YAAM,WAAW,QAAQ,IAAI,IAAI,IAAI,QAAQ,IAAI,SAAS,CAAC;AAC3D,kBAAY,cAAc,OAAO,KAAK,QAAQ,GAAG,QAAQ;AAAA,IAC3D;AAAA,EACF,WAAW,eAAe,iBAAiB,wBAAwB,GAAG,GAAG;AACvE,gBAAY,cAAc,OAAO,KAAK,IAAI,IAAI;AAAA,EAChD,WAAW,eAAe,iBAAiB,2BAA2B,GAAG,GAAG;AAC1E,gBAAY,cAAc,OAAO,KAAK,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC;AAAA,EAC1E;AACA,MAAI,UAAW,EAAC,OAAO,KAAK,MAAM,KAAK,aAAa,SAAS;AAC7D,SAAO;AACT;AAIA,IAAI,eAAe,IAAI,UAAW,YAAY;AAC9C,SAAS,mBAAmB,KAAK,KAAK,QAAQ,GAAG;AAC/C,QAAM,UAAU,IAAI,YAAY,UAAU,IAAI;AAC9C,QAAO,UAAS,IAAI,GAAG,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9C,UAAM,QAAQ,IAAI,MAAM,CAAC;AACzB,aAAS,OAAO,GAAG,IAAI,KAAK,IAAI,SAAS,IAAI,CAAC,GAAG,OAAO,GAAG,QAAQ;AACjE,UAAI,IAAI,MAAM,IAAI,KAAK,OAAO;AAC5B,YAAI,OAAO;AACX,kBAAU,MAAM;AAChB,iBAAS;AAAA,MACX;AAAA,IACF;AACA,MAAE,OAAO,MAAM;AACf,QAAI,IAAI,WAAW,IAAI,MAAM,CAAC,EAAE,WAAW,KAAK;AAC9C,yBAAmB,IAAI,MAAM,CAAC,GAAG,OAAO,SAAS,GAAG,CAAC;AAAA,QAClD,OAAM,aAAa,GAAG,MAAM,QAAQ,MAAM,GAAG,SAAS,CAAC;AAC5D,cAAU,MAAM;AAAA,EAClB;AACF;AACA,SAAS,UAAU,OAAO,UAAU;AAClC,MAAI;AACJ,QAAM,QAAQ,CAAC,MAAM,QAAQ;AAC3B,QAAI,KAAK,KAAK,KAAK,aAAa;AAC9B,WAAK,SAAS,OAAO,MAAM,KAAK,EAAE;AAAA,EACtC;AACA,MAAI,CAAC,SAAU,OAAM,IAAI,YAAY,KAAK;AAAA,WACjC,SAAS,OAAO,MAAM;AAC7B,uBAAmB,SAAS,KAAK,MAAM,KAAK,GAAG,KAAK;AACtD,SAAO;AACT;AACA,SAAS,SAAS,OAAO,OAAO,UAAU,IAAI;AAC5C,QAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,MAAI,CAAC,IAAI,SAAU,QAAO;AAC1B,MAAI,CAAC,GAAI,MAAK,MAAM;AACpB,QAAM,UAAU,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,SAAQ,KAAK,CAAC;AACnD,WAAS,IAAI,GAAG,IAAI,IAAI,SAAS,QAAQ,KAAK;AAC5C,UAAM,OAAO,IAAI,SAAS,CAAC;AAC3B,QAAI,KAAK,QAAQ,aAAa;AAC5B,YAAM,OAAO,MAAM,OAAO,KAAK,GAAG;AAClC,UAAI,CAAC,KAAM;AACX,YAAM,QAAQ,KAAK;AACnB,eAAS,IAAI,GAAG,IAAI,MAAM,SAAS,IAAK,SAAQ,KAAK,MAAM,CAAC,KAAK,KAAK;AACtE,SAAG;AAAA,QACD,GAAG,QAAQ,IAAI,WAAW,IAAI,KAAK,GAAG;AAAA,QACtC;AAAA,QACA,cAAc,OAAO,MAAM,UAAU,KAAK,GAAG,KAAK,CAAC;AAAA,MACrD;AAAA,IACF,WAAW,KAAK,QAAQ,WAAW;AACjC,cAAQ,KAAK,GAAG,KAAK,KAAK;AAAA,IAC5B,WAAW,KAAK,QAAQ,oBAAoB;AAC1C,YAAM,OAAO,MAAM,OAAO,KAAK,GAAG;AAClC,UAAI,CAAC,KAAM;AACX,SAAG,cAAc,GAAG,QAAQ,IAAI,WAAW,IAAI,KAAK,GAAG,GAAG,MAAM;AAAA,QAC9D,GAAG,KAAK;AAAA,QACR,SAAS,KAAK,MAAM,UAAU,KAAK;AAAA,MACrC,CAAC;AAAA,IACH,WAAW,KAAK,QAAQ,qBAAqB;AAC3C,YAAM,OAAO,MAAM,OAAO,KAAK,GAAG;AAClC,UAAI,CAAC,KAAM;AACX,SAAG,cAAc,GAAG,QAAQ,IAAI,WAAW,IAAI,KAAK,GAAG,GAAG,MAAM;AAAA,QAC9D,GAAG,KAAK;AAAA,QACR,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH,WAAW,KAAK,QAAQ,cAAc;AACpC,YAAM,MAAM,GAAG,QAAQ,IAAI,QAAQ;AACnC,SAAG,OAAO,KAAK,MAAM,MAAM,QAAQ;AAAA,IACrC;AAAA,EACF;AACA,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAClC,QAAI,QAAQ,CAAC,GAAG;AACd,UAAI,SAAS,KAAM,SAAQ;AAC3B,aAAO;AAAA,IACT;AACF,WAAS,IAAI,GAAG,MAAM,WAAW,GAAG,IAAI,IAAI,QAAQ,KAAK;AACvD,UAAM,MAAM,MAAM,MAAM,CAAC;AACzB,UAAM,MAAM,MAAM,IAAI;AACtB,UAAM,MAAM,QAAQ,CAAC;AACrB,QAAI,MAAM,GAAG;AACX,UAAI,OAAO;AACX,UAAI,IAAI,YAAY;AAClB,eAAO,IAAI,WAAW,KAAK,KAAK;AAAA,MAClC;AACA,YAAM,QAAQ,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAM,OAAO,eAAe,MAAM,MAAM,EAAE,IAAI,EAAE,cAAc;AAC9D,YAAI,KAAM,OAAM,KAAK,IAAI;AAAA,MAC3B;AACA,YAAM,QAAQ,KAAK,KAAK,SAAS,IAAI,MAAM,QAAQ,IAAI,MAAM,IAAI,MAAM;AACvE,SAAG,OAAO,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK;AAAA,IACvC;AACA,UAAM;AAAA,EACR;AACA,SAAO,GAAG,QAAQ,cAAc,EAAE,WAAW,KAAK,CAAC;AACrD;AAkBA,SAAS,aAAa,OAAO;AAC3B,QAAM,MAAM,MAAM;AAClB,QAAM,OAAO,cAAc,KAAK;AAChC,QAAM,QAAQ,KAAK,KAAK,EAAE;AAC1B,QAAM,aAAa,KAAK,MAAM,EAAE;AAChC,QAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,QAAM,OAAO,eAAe,gBAAgB,IAAI;AAAA,IAC9C,IAAI,YAAY,MAAM;AAAA,IACtB,IAAI,UAAU,MAAM;AAAA,EACtB,IAAI,IAAI,SAAS,KAAK,MAAM,UAAU;AACtC,SAAO,EAAE,GAAG,MAAM,YAAY,KAAK,MAAM;AAC3C;AACA,SAAS,UAAU,IAAI,EAAE,KAAK,YAAY,MAAM,GAAG,KAAK;AACtD,MAAI,YAAY,MAAM,IAAI,KAAK;AAC/B,MAAI,eAAe,KAAK,OAAO,MAAM,SAAS,GAAG;AAC/C,gBAAY,OAAO,KAAK,OAAO,IAAI,QAAQ,OAAO;AAAA,EACpD;AACA,WAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,OAAO;AACzC,UAAM,QAAQ,MAAM,IAAI,QAAQ;AAChC,QAAI,MAAM,KAAK,MAAM,IAAI,SAAS,IAAI,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,KAAK,GAAG;AACtE,YAAM,MAAM,IAAI,IAAI,KAAK;AACzB,YAAM,OAAO,MAAM,OAAO,GAAG;AAC7B,SAAG;AAAA,QACD,GAAG,QAAQ,IAAI,aAAa,GAAG;AAAA,QAC/B;AAAA,QACA,WAAW,KAAK,OAAO,MAAM,IAAI,SAAS,GAAG,CAAC;AAAA,MAChD;AACA,aAAO,KAAK,MAAM,UAAU;AAAA,IAC9B,OAAO;AACL,YAAM,OAAO,aAAa,OAAO,eAAe,MAAM,KAAK,MAAM,EAAE,OAAO,MAAM,OAAO,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE;AACnH,YAAM,MAAM,IAAI,WAAW,KAAK,KAAK,KAAK;AAC1C,SAAG,OAAO,GAAG,QAAQ,IAAI,aAAa,GAAG,GAAG,KAAK,cAAc,CAAC;AAAA,IAClE;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,OAAO,UAAU;AACxC,MAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,MAAI,UAAU;AACZ,UAAM,OAAO,aAAa,KAAK;AAC/B,aAAS,UAAU,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC;AAAA,EAC/C;AACA,SAAO;AACT;AACA,SAAS,eAAe,OAAO,UAAU;AACvC,MAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,MAAI,UAAU;AACZ,UAAM,OAAO,aAAa,KAAK;AAC/B,aAAS,UAAU,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AAAA,EAChD;AACA,SAAO;AACT;AACA,SAAS,aAAa,IAAI,EAAE,KAAK,OAAO,WAAW,GAAG,KAAK;AACzD,QAAM,WAAW,GAAG,QAAQ,KAAK;AACjC,WAAS,MAAM,GAAG,MAAM,IAAI,UAAU;AACpC,UAAM,QAAQ,MAAM,IAAI,QAAQ;AAChC,UAAM,MAAM,IAAI,IAAI,KAAK;AACzB,UAAM,OAAO,MAAM,OAAO,GAAG;AAC7B,UAAM,QAAQ,KAAK;AACnB,QAAI,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,KAAK,OAAO,MAAM,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,CAAC,KAAK,KAAK;AAC5F,SAAG;AAAA,QACD,GAAG,QAAQ,MAAM,QAAQ,EAAE,IAAI,aAAa,GAAG;AAAA,QAC/C;AAAA,QACA,cAAc,OAAO,MAAM,IAAI,SAAS,GAAG,CAAC;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,YAAM,QAAQ,GAAG,QAAQ,MAAM,QAAQ,EAAE,IAAI,aAAa,GAAG;AAC7D,SAAG,OAAO,OAAO,QAAQ,KAAK,QAAQ;AAAA,IACxC;AACA,WAAO,MAAM;AAAA,EACf;AACF;AACA,SAAS,aAAa,OAAO,UAAU;AACrC,MAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,MAAI,UAAU;AACZ,UAAM,OAAO,aAAa,KAAK;AAC/B,UAAM,KAAK,MAAM;AACjB,QAAI,KAAK,QAAQ,KAAK,KAAK,SAAS,KAAK,IAAI,MAAO,QAAO;AAC3D,aAAS,IAAI,KAAK,QAAQ,KAAK,KAAK;AAClC,mBAAa,IAAI,MAAM,CAAC;AACxB,UAAI,KAAK,KAAK,KAAM;AACpB,YAAM,QAAQ,KAAK,aAAa,GAAG,IAAI,OAAO,KAAK,aAAa,CAAC,IAAI,GAAG;AACxE,UAAI,CAAC,OAAO;AACV,cAAM,WAAW,gBAAgB;AAAA,MACnC;AACA,WAAK,QAAQ;AACb,WAAK,MAAM,SAAS,IAAI,KAAK;AAAA,IAC/B;AACA,aAAS,EAAE;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,YAAY,KAAK,OAAO,KAAK;AACpC,MAAI;AACJ,QAAM,aAAa,eAAe,MAAM,KAAK,MAAM,EAAE;AACrD,WAAS,MAAM,GAAG,MAAM,IAAI,OAAO;AACjC,UAAM,KAAK,MAAM,OAAO,IAAI,IAAI,MAAM,MAAM,IAAI,KAAK,CAAC,MAAM,OAAO,SAAS,GAAG,SAAS;AACtF,aAAO;AACX,SAAO;AACT;AACA,SAAS,OAAO,IAAI,EAAE,KAAK,YAAY,MAAM,GAAG,KAAK;AACnD,MAAI;AACJ,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,KAAK,IAAK,WAAU,MAAM,MAAM,CAAC,EAAE;AACvD,QAAM,QAAQ,CAAC;AACf,MAAI,SAAS,MAAM,IAAI,KAAK;AAC5B,MAAI,YAAY,KAAK,OAAO,MAAM,MAAM;AACtC,aAAS,OAAO,KAAK,OAAO,IAAI,SAAS,OAAO;AAClD,WAAS,MAAM,GAAG,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI,OAAO,OAAO,SAAS;AAC1E,QAAI,MAAM,KAAK,MAAM,IAAI,UAAU,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,GAAG;AAC/E,YAAM,MAAM,IAAI,IAAI,KAAK;AACzB,YAAM,QAAQ,MAAM,OAAO,GAAG,EAAE;AAChC,SAAG,cAAc,aAAa,KAAK,MAAM;AAAA,QACvC,GAAG;AAAA,QACH,SAAS,MAAM,UAAU;AAAA,MAC3B,CAAC;AACD,aAAO,MAAM,UAAU;AAAA,IACzB,OAAO;AACL,YAAM,OAAO,UAAU,OAAO,eAAe,MAAM,KAAK,MAAM,EAAE,QAAQ,KAAK,MAAM,OAAO,IAAI,IAAI,QAAQ,SAAS,IAAI,KAAK,CAAC,MAAM,OAAO,SAAS,GAAG;AACtJ,YAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,cAAc;AACxD,UAAI,KAAM,OAAM,KAAK,IAAI;AAAA,IAC3B;AAAA,EACF;AACA,KAAG,OAAO,QAAQ,eAAe,MAAM,KAAK,MAAM,EAAE,IAAI,OAAO,MAAM,KAAK,CAAC;AAC3E,SAAO;AACT;AACA,SAAS,aAAa,OAAO,UAAU;AACrC,MAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,MAAI,UAAU;AACZ,UAAM,OAAO,aAAa,KAAK;AAC/B,aAAS,OAAO,MAAM,IAAI,MAAM,KAAK,GAAG,CAAC;AAAA,EAC3C;AACA,SAAO;AACT;AACA,SAAS,YAAY,OAAO,UAAU;AACpC,MAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,MAAI,UAAU;AACZ,UAAM,OAAO,aAAa,KAAK;AAC/B,aAAS,OAAO,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC;AAAA,EAC9C;AACA,SAAO;AACT;AACA,SAAS,UAAU,IAAI,EAAE,KAAK,OAAO,WAAW,GAAG,KAAK;AACtD,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,KAAK,IAAK,WAAU,MAAM,MAAM,CAAC,EAAE;AACvD,QAAM,UAAU,SAAS,MAAM,MAAM,GAAG,EAAE;AAC1C,QAAM,UAAU,GAAG,QAAQ,KAAK;AAChC,KAAG,OAAO,SAAS,YAAY,UAAU,UAAU;AACnD,QAAM,OAAuB,oBAAI,IAAI;AACrC,WAAS,MAAM,GAAG,QAAQ,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,OAAO,SAAS;AAC1E,UAAM,MAAM,IAAI,IAAI,KAAK;AACzB,QAAI,KAAK,IAAI,GAAG,EAAG;AACnB,SAAK,IAAI,GAAG;AACZ,QAAI,MAAM,KAAK,OAAO,IAAI,IAAI,QAAQ,IAAI,KAAK,GAAG;AAChD,YAAM,QAAQ,MAAM,OAAO,GAAG,EAAE;AAChC,SAAG,cAAc,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,MAAM,UAAU,GAAG,MAAM;AAAA,QACtE,GAAG;AAAA,QACH,SAAS,MAAM,UAAU;AAAA,MAC3B,CAAC;AACD,aAAO,MAAM,UAAU;AAAA,IACzB,WAAW,MAAM,IAAI,UAAU,OAAO,IAAI,IAAI,QAAQ,IAAI,KAAK,GAAG;AAChE,YAAM,OAAO,MAAM,OAAO,GAAG;AAC7B,YAAM,QAAQ,KAAK;AACnB,YAAM,OAAO,KAAK,KAAK;AAAA,QACrB,EAAE,GAAG,OAAO,SAAS,KAAK,MAAM,UAAU,EAAE;AAAA,QAC5C,KAAK;AAAA,MACP;AACA,YAAM,SAAS,IAAI,WAAW,MAAM,GAAG,KAAK,KAAK;AACjD,SAAG,OAAO,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,aAAa,MAAM,GAAG,IAAI;AAClE,aAAO,MAAM,UAAU;AAAA,IACzB;AAAA,EACF;AACF;AACA,SAAS,UAAU,OAAO,UAAU;AAClC,MAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,MAAI,UAAU;AACZ,UAAM,OAAO,aAAa,KAAK,GAAG,KAAK,MAAM;AAC7C,QAAI,KAAK,OAAO,KAAK,KAAK,UAAU,KAAK,IAAI,OAAQ,QAAO;AAC5D,aAAS,IAAI,KAAK,SAAS,KAAK,KAAK;AACnC,gBAAU,IAAI,MAAM,CAAC;AACrB,UAAI,KAAK,KAAK,IAAK;AACnB,YAAM,QAAQ,KAAK,aAAa,GAAG,IAAI,OAAO,KAAK,aAAa,CAAC,IAAI,GAAG;AACxE,UAAI,CAAC,OAAO;AACV,cAAM,WAAW,gBAAgB;AAAA,MACnC;AACA,WAAK,QAAQ;AACb,WAAK,MAAM,SAAS,IAAI,KAAK,KAAK;AAAA,IACpC;AACA,aAAS,EAAE;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,QAAQ,MAAM;AACrB,QAAM,IAAI,KAAK;AACf,SAAO,EAAE,cAAc,KAAK,EAAE,MAAM,CAAC,EAAE,eAAe,EAAE,MAAM,CAAC,EAAE,cAAc;AACjF;AACA,SAAS,sBAAsB,EAAE,OAAO,QAAQ,IAAI,GAAG,MAAM;AAC3D,MAAI,WAAW,KAAK,MAAM,QAAQ,KAAK,MAAM,YAAY;AACzD,MAAI,eAAe,KAAK,SAAS,KAAK,QAAQ,KAAK,MAAM,aAAa,YAAY,KAAK,QAAQ,KAAK,OAAO;AAC3G,WAAS,IAAI,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK;AAC3C,QAAI,KAAK,OAAO,KAAK,IAAI,SAAS,KAAK,IAAI,YAAY,CAAC,KAAK,KAAK,QAAQ,SAAS,IAAI,UAAU,KAAK,IAAI,aAAa,CAAC;AACtH,aAAO;AACT,iBAAa;AACb,kBAAc;AAAA,EAChB;AACA,WAAS,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,KAAK;AAC3C,QAAI,KAAK,MAAM,KAAK,IAAI,QAAQ,KAAK,IAAI,WAAW,KAAK,KAAK,KAAK,SAAS,UAAU,IAAI,WAAW,KAAK,IAAI,cAAc,KAAK;AAC/H,aAAO;AACT;AACA;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,WAAW,OAAO,UAAU;AACnC,QAAM,MAAM,MAAM;AAClB,MAAI,EAAE,eAAe,kBAAkB,IAAI,YAAY,OAAO,IAAI,UAAU;AAC1E,WAAO;AACT,QAAM,OAAO,aAAa,KAAK,GAAG,EAAE,IAAI,IAAI;AAC5C,MAAI,sBAAsB,KAAK,IAAI,EAAG,QAAO;AAC7C,MAAI,UAAU;AACZ,UAAM,KAAK,MAAM;AACjB,UAAM,OAAO,CAAC;AACd,QAAI,UAAU,SAAU;AACxB,QAAI;AACJ,QAAI;AACJ,aAAS,MAAM,KAAK,KAAK,MAAM,KAAK,QAAQ,OAAO;AACjD,eAAS,MAAM,KAAK,MAAM,MAAM,KAAK,OAAO,OAAO;AACjD,cAAM,UAAU,IAAI,IAAI,MAAM,IAAI,QAAQ,GAAG;AAC7C,cAAM,OAAO,KAAK,MAAM,OAAO,OAAO;AACtC,YAAI,KAAK,OAAO,KAAK,CAAC,KAAM;AAC5B,aAAK,OAAO,IAAI;AAChB,YAAI,aAAa,MAAM;AACrB,sBAAY;AACZ,uBAAa;AAAA,QACf,OAAO;AACL,cAAI,CAAC,QAAQ,IAAI,EAAG,WAAU,QAAQ,OAAO,KAAK,OAAO;AACzD,gBAAM,SAAS,GAAG,QAAQ,IAAI,UAAU,KAAK,UAAU;AACvD,aAAG,OAAO,QAAQ,SAAS,KAAK,QAAQ;AAAA,QAC1C;AAAA,MACF;AAAA,IACF;AACA,QAAI,aAAa,QAAQ,cAAc,MAAM;AAC3C,aAAO;AAAA,IACT;AACA,OAAG,cAAc,YAAY,KAAK,YAAY,MAAM;AAAA,MAClD,GAAG;AAAA,QACD,WAAW;AAAA,QACX,WAAW,MAAM;AAAA,QACjB,KAAK,QAAQ,KAAK,OAAO,WAAW,MAAM;AAAA,MAC5C;AAAA,MACA,SAAS,KAAK,SAAS,KAAK;AAAA,IAC9B,CAAC;AACD,QAAI,QAAQ,MAAM;AAChB,YAAM,MAAM,YAAY,IAAI,WAAW,QAAQ;AAC/C,YAAM,QAAQ,QAAQ,UAAU,IAAI,YAAY,IAAI;AACpD,SAAG,YAAY,QAAQ,KAAK,YAAY,MAAM,KAAK,YAAY,OAAO;AAAA,IACxE;AACA,OAAG;AAAA,MACD,IAAI,cAAc,GAAG,IAAI,QAAQ,YAAY,KAAK,UAAU,CAAC;AAAA,IAC/D;AACA,aAAS,EAAE;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,UAAU,OAAO,UAAU;AAClC,QAAM,YAAY,eAAe,MAAM,MAAM;AAC7C,SAAO,kBAAkB,CAAC,EAAE,KAAK,MAAM;AACrC,WAAO,UAAU,KAAK,KAAK,KAAK,SAAS;AAAA,EAC3C,CAAC,EAAE,OAAO,QAAQ;AACpB;AACA,SAAS,kBAAkB,aAAa;AACtC,SAAO,CAAC,OAAO,aAAa;AAC1B,QAAI;AACJ,UAAM,MAAM,MAAM;AAClB,QAAI;AACJ,QAAI;AACJ,QAAI,EAAE,eAAe,gBAAgB;AACnC,iBAAW,aAAa,IAAI,KAAK;AACjC,UAAI,CAAC,SAAU,QAAO;AACtB,iBAAW,KAAK,WAAW,IAAI,KAAK,MAAM,OAAO,SAAS,GAAG;AAAA,IAC/D,OAAO;AACL,UAAI,IAAI,YAAY,OAAO,IAAI,UAAU,IAAK,QAAO;AACrD,iBAAW,IAAI,YAAY;AAC3B,gBAAU,IAAI,YAAY;AAAA,IAC5B;AACA,QAAI,YAAY,QAAQ,WAAW,MAAM;AACvC,aAAO;AAAA,IACT;AACA,QAAI,SAAS,MAAM,WAAW,KAAK,SAAS,MAAM,WAAW,GAAG;AAC9D,aAAO;AAAA,IACT;AACA,QAAI,UAAU;AACZ,UAAI,YAAY,SAAS;AACzB,YAAM,QAAQ,CAAC;AACf,YAAM,WAAW,UAAU;AAC3B,UAAI,UAAU,UAAU,EAAG,aAAY,EAAE,GAAG,WAAW,SAAS,EAAE;AAClE,UAAI,UAAU,UAAU,EAAG,aAAY,EAAE,GAAG,WAAW,SAAS,EAAE;AAClE,YAAM,OAAO,aAAa,KAAK,GAAG,KAAK,MAAM;AAC7C,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,MAAM;AAC1C,cAAM;AAAA,UACJ,WAAW;AAAA,YACT,GAAG;AAAA,YACH,UAAU,YAAY,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI;AAAA,UACtD,IAAI;AAAA,QACN;AACF,UAAI;AACJ,eAAS,MAAM,KAAK,KAAK,MAAM,KAAK,QAAQ,OAAO;AACjD,YAAI,MAAM,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,KAAK,KAAK;AACxD,YAAI,OAAO,KAAK,IAAK,QAAO,SAAS;AACrC,iBAAS,MAAM,KAAK,MAAM,IAAI,GAAG,MAAM,KAAK,OAAO,OAAO,KAAK;AAC7D,cAAI,OAAO,KAAK,QAAQ,OAAO,KAAK,IAAK;AACzC,aAAG;AAAA,YACD,WAAW,GAAG,QAAQ,IAAI,MAAM,KAAK,YAAY,CAAC;AAAA,YAClD,YAAY,EAAE,MAAM,UAAU,KAAK,IAAI,CAAC,EAAE,cAAc,MAAM,CAAC,CAAC;AAAA,UAClE;AAAA,QACF;AAAA,MACF;AACA,SAAG;AAAA,QACD;AAAA,QACA,YAAY,EAAE,MAAM,UAAU,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC;AAAA,QAC7D,MAAM,CAAC;AAAA,MACT;AACA,UAAI,eAAe;AACjB,WAAG;AAAA,UACD,IAAI;AAAA,YACF,GAAG,IAAI,QAAQ,IAAI,YAAY,GAAG;AAAA,YAClC,WAAW,GAAG,IAAI,QAAQ,QAAQ,IAAI;AAAA,UACxC;AAAA,QACF;AACF,eAAS,EAAE;AAAA,IACb;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,YAAY,MAAM,OAAO;AAChC,SAAO,SAAS,OAAO,UAAU;AAC/B,QAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,UAAM,QAAQ,cAAc,KAAK;AACjC,QAAI,MAAM,UAAU,MAAM,IAAI,MAAM,MAAO,QAAO;AAClD,QAAI,UAAU;AACZ,YAAM,KAAK,MAAM;AACjB,UAAI,MAAM,qBAAqB;AAC7B,cAAM,UAAU,YAAY,CAAC,MAAM,QAAQ;AACzC,cAAI,KAAK,MAAM,IAAI,MAAM;AACvB,eAAG,cAAc,KAAK,MAAM;AAAA,cAC1B,GAAG,KAAK;AAAA,cACR,CAAC,IAAI,GAAG;AAAA,YACV,CAAC;AAAA,QACL,CAAC;AAAA;AAED,WAAG,cAAc,MAAM,KAAK,MAAM;AAAA,UAChC,GAAG,MAAM,UAAU;AAAA,UACnB,CAAC,IAAI,GAAG;AAAA,QACV,CAAC;AACH,eAAS,EAAE;AAAA,IACb;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,wBAAwB,MAAM;AACrC,SAAO,SAAS,OAAO,UAAU;AAC/B,QAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,QAAI,UAAU;AACZ,YAAM,QAAQ,eAAe,MAAM,MAAM;AACzC,YAAM,OAAO,aAAa,KAAK,GAAG,KAAK,MAAM;AAC7C,YAAM,QAAQ,KAAK,IAAI;AAAA,QACrB,QAAQ,WAAW;AAAA,UACjB,MAAM,KAAK;AAAA,UACX,KAAK;AAAA,UACL,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK,IAAI;AAAA,QACnB,IAAI,QAAQ,QAAQ;AAAA,UAClB,MAAM;AAAA,UACN,KAAK,KAAK;AAAA,UACV,OAAO,KAAK,IAAI;AAAA,UAChB,QAAQ,KAAK;AAAA,QACf,IAAI;AAAA,MACN;AACA,YAAM,QAAQ,MAAM,IAAI,CAAC,QAAQ,KAAK,MAAM,OAAO,GAAG,CAAC;AACvD,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAChC,YAAI,MAAM,CAAC,EAAE,QAAQ,MAAM;AACzB,aAAG;AAAA,YACD,KAAK,aAAa,MAAM,CAAC;AAAA,YACzB,MAAM;AAAA,YACN,MAAM,CAAC,EAAE;AAAA,UACX;AACJ,UAAI,GAAG,MAAM,UAAU;AACrB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAChC,aAAG;AAAA,YACD,KAAK,aAAa,MAAM,CAAC;AAAA,YACzB,MAAM;AAAA,YACN,MAAM,CAAC,EAAE;AAAA,UACX;AACJ,eAAS,EAAE;AAAA,IACb;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,sBAAsB,MAAM,MAAM,OAAO;AAChD,QAAM,gBAAgB,KAAK,IAAI,YAAY;AAAA,IACzC,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO,QAAQ,QAAQ,KAAK,IAAI,QAAQ;AAAA,IACxC,QAAQ,QAAQ,WAAW,KAAK,IAAI,SAAS;AAAA,EAC/C,CAAC;AACD,WAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,UAAM,OAAO,KAAK,MAAM,OAAO,cAAc,CAAC,CAAC;AAC/C,QAAI,QAAQ,KAAK,SAAS,MAAM,aAAa;AAC3C,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,MAAM,SAAS;AACnC,YAAU,WAAW,EAAE,oBAAoB,MAAM;AACjD,MAAI,QAAQ,mBAAoB,QAAO,wBAAwB,IAAI;AACnE,SAAO,SAAS,OAAO,UAAU;AAC/B,QAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,QAAI,UAAU;AACZ,YAAM,QAAQ,eAAe,MAAM,MAAM;AACzC,YAAM,OAAO,aAAa,KAAK,GAAG,KAAK,MAAM;AAC7C,YAAM,qBAAqB,sBAAsB,OAAO,MAAM,KAAK;AACnE,YAAM,wBAAwB;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,kBAAkB,SAAS,WAAW,qBAAqB,SAAS,QAAQ,wBAAwB;AAC1G,YAAM,oBAAoB,kBAAkB,IAAI;AAChD,YAAM,YAAY,QAAQ,WAAW;AAAA,QACnC,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ,KAAK,IAAI;AAAA,MACnB,IAAI,QAAQ,QAAQ;AAAA,QAClB,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO,KAAK,IAAI;AAAA,QAChB,QAAQ;AAAA,MACV,IAAI;AACJ,YAAM,UAAU,QAAQ,WAAW,wBAAwB,MAAM,OAAO,MAAM,cAAc,QAAQ,QAAQ,qBAAqB,MAAM,OAAO,MAAM,cAAc,MAAM;AACxK,WAAK,IAAI,YAAY,SAAS,EAAE,QAAQ,CAAC,oBAAoB;AAC3D,cAAM,UAAU,kBAAkB,KAAK;AACvC,cAAM,OAAO,GAAG,IAAI,OAAO,OAAO;AAClC,YAAI,MAAM;AACR,aAAG,cAAc,SAAS,SAAS,KAAK,KAAK;AAAA,QAC/C;AAAA,MACF,CAAC;AACD,eAAS,EAAE;AAAA,IACb;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAI,kBAAkB,aAAa,OAAO;AAAA,EACxC,oBAAoB;AACtB,CAAC;AACD,IAAI,qBAAqB,aAAa,UAAU;AAAA,EAC9C,oBAAoB;AACtB,CAAC;AACD,IAAI,mBAAmB,aAAa,QAAQ;AAAA,EAC1C,oBAAoB;AACtB,CAAC;AACD,SAAS,aAAa,OAAO,KAAK;AAChC,MAAI,MAAM,GAAG;AACX,UAAM,SAAS,MAAM;AACrB,QAAI,OAAQ,QAAO,MAAM,MAAM,OAAO;AACtC,aAAS,MAAM,MAAM,MAAM,EAAE,IAAI,GAAG,SAAS,MAAM,OAAO,GAAG,OAAO,GAAG,OAAO;AAC5E,YAAM,UAAU,MAAM,KAAK,EAAE,EAAE,MAAM,GAAG;AACxC,YAAM,YAAY,QAAQ;AAC1B,UAAI,WAAW;AACb,eAAO,SAAS,IAAI,UAAU;AAAA,MAChC;AACA,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF,OAAO;AACL,QAAI,MAAM,MAAM,IAAI,MAAM,OAAO,aAAa,GAAG;AAC/C,aAAO,MAAM,MAAM,MAAM,UAAU;AAAA,IACrC;AACA,UAAM,QAAQ,MAAM,KAAK,EAAE;AAC3B,aAAS,MAAM,MAAM,WAAW,EAAE,GAAG,WAAW,MAAM,MAAM,GAAG,MAAM,MAAM,YAAY,OAAO;AAC5F,YAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,UAAI,QAAQ,WAAY,QAAO,WAAW;AAC1C,kBAAY,QAAQ;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,SAAS,OAAO,UAAU;AAC/B,QAAI,CAAC,UAAU,KAAK,EAAG,QAAO;AAC9B,UAAM,OAAO,aAAa,cAAc,KAAK,GAAG,SAAS;AACzD,QAAI,QAAQ,KAAM,QAAO;AACzB,QAAI,UAAU;AACZ,YAAM,QAAQ,MAAM,IAAI,QAAQ,IAAI;AACpC;AAAA,QACE,MAAM,GAAG,aAAa,cAAe,QAAQ,OAAO,gBAAgB,KAAK,CAAC,CAAC,EAAE,eAAe;AAAA,MAC9F;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,YAAY,OAAO,UAAU;AACpC,QAAM,OAAO,MAAM,UAAU;AAC7B,WAAS,IAAI,KAAK,OAAO,IAAI,GAAG,KAAK;AACnC,UAAM,OAAO,KAAK,KAAK,CAAC;AACxB,QAAI,KAAK,KAAK,KAAK,aAAa,SAAS;AACvC,UAAI;AACF;AAAA,UACE,MAAM,GAAG,OAAO,KAAK,OAAO,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,EAAE,eAAe;AAAA,QAChE;AACF,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,OAAO,UAAU;AAC5C,QAAM,MAAM,MAAM;AAClB,MAAI,EAAE,eAAe,eAAgB,QAAO;AAC5C,MAAI,UAAU;AACZ,UAAM,KAAK,MAAM;AACjB,UAAM,cAAc,eAAe,MAAM,MAAM,EAAE,KAAK,cAAc,EAAE;AACtE,QAAI,YAAY,CAAC,MAAM,QAAQ;AAC7B,UAAI,CAAC,KAAK,QAAQ,GAAG,WAAW;AAC9B,WAAG;AAAA,UACD,GAAG,QAAQ,IAAI,MAAM,CAAC;AAAA,UACtB,GAAG,QAAQ,IAAI,MAAM,KAAK,WAAW,CAAC;AAAA,UACtC,IAAI,MAAO,aAAa,GAAG,CAAC;AAAA,QAC9B;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,WAAY,UAAS,EAAE;AAAA,EAChC;AACA,SAAO;AACT;AAKA,SAAS,YAAY,OAAO;AAC1B,MAAI,CAAC,MAAM,KAAM,QAAO;AACxB,MAAI,EAAE,SAAS,WAAW,QAAQ,IAAI;AACtC,SAAO,QAAQ,cAAc,MAAM,YAAY,KAAK,UAAU,KAAK,QAAQ,MAAM,CAAC,EAAE,KAAK,KAAK,aAAa,UAAU;AACnH;AACA;AACA,cAAU,QAAQ,MAAM,CAAC,EAAE;AAAA,EAC7B;AACA,QAAM,QAAQ,QAAQ,MAAM,CAAC;AAC7B,QAAM,OAAO,MAAM,KAAK,KAAK;AAC7B,QAAM,SAAS,MAAM,KAAK,QAAQ,OAAO,CAAC;AAC1C,MAAI,QAAQ,OAAO;AACjB,aAAS,IAAI,GAAG,IAAI,QAAQ,YAAY,KAAK;AAC3C,UAAI,QAAQ,QAAQ,MAAM,CAAC,EAAE;AAC7B,YAAM,OAAO,IAAI,IAAI,KAAK,IAAI,GAAG,YAAY,CAAC;AAC9C,YAAM,QAAQ,IAAI,QAAQ,aAAa,IAAI,IAAI,KAAK,IAAI,GAAG,UAAU,CAAC;AACtE,UAAI,QAAQ;AACV,gBAAQ;AAAA,UACN,eAAe,MAAM,EAAE;AAAA,UACvB,IAAI,MAAO,OAAO,MAAM,KAAK;AAAA,QAC/B,EAAE;AACJ,WAAK,KAAK,KAAK;AAAA,IACjB;AAAA,EACF,WAAW,QAAQ,UAAU,QAAQ,eAAe;AAClD,SAAK;AAAA,MACH,aAAa,UAAU;AAAA,QACrB,eAAe,MAAM,EAAE;AAAA,QACvB,IAAI,MAAO,SAAS,WAAW,OAAO;AAAA,MACxC,EAAE,UAAU;AAAA,IACd;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACA,SAAO,kBAAkB,QAAQ,IAAI;AACvC;AACA,SAAS,kBAAkB,QAAQ,MAAM;AACvC,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,MAAM,KAAK,CAAC;AAClB,aAAS,IAAI,IAAI,aAAa,GAAG,KAAK,GAAG,KAAK;AAC5C,YAAM,EAAE,SAAS,QAAQ,IAAI,IAAI,MAAM,CAAC,EAAE;AAC1C,eAAS,IAAI,GAAG,IAAI,IAAI,SAAS;AAC/B,eAAO,CAAC,KAAK,OAAO,CAAC,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AACA,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAK,SAAQ,KAAK,IAAI,OAAO,OAAO,CAAC,CAAC;AACzE,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,KAAK,KAAK,OAAQ,MAAK,KAAK,SAAU,KAAK;AAC/C,QAAI,OAAO,CAAC,IAAI,OAAO;AACrB,YAAM,QAAQ,eAAe,MAAM,EAAE,KAAK,cAAc;AACxD,YAAM,QAAQ,CAAC;AACf,eAAS,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,KAAK;AACtC,cAAM,KAAK,KAAK;AAAA,MAClB;AACA,WAAK,CAAC,IAAI,KAAK,CAAC,EAAE,OAAO,SAAU,KAAK,KAAK,CAAC;AAAA,IAChD;AAAA,EACF;AACA,SAAO,EAAE,QAAQ,KAAK,QAAQ,OAAO,KAAK;AAC5C;AACA,SAAS,SAAS,UAAU,OAAO;AACjC,QAAM,OAAO,SAAS,cAAc;AACpC,QAAM,KAAK,IAAI,UAAU,IAAI,EAAE,QAAQ,GAAG,KAAK,QAAQ,MAAM,KAAK;AAClE,SAAO,GAAG;AACZ;AACA,SAAS,UAAU,EAAE,OAAO,QAAQ,KAAK,GAAG,UAAU,WAAW;AAC/D,MAAI,SAAS,UAAU;AACrB,UAAM,QAAQ,CAAC;AACf,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,GAAG,MAAM,KAAK,QAAQ,OAAO;AAC1C,YAAM,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC;AACjC,eAAS,MAAM,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,MAAM,UAAU,KAAK;AAC1D,YAAI,OAAO,KAAK,MAAM,IAAI,KAAK,UAAU;AACzC,YAAI,MAAM,KAAK,MAAM,UAAU;AAC7B,iBAAO,KAAK,KAAK;AAAA,YACf;AAAA,cACE,KAAK;AAAA,cACL,KAAK,MAAM;AAAA,cACX,MAAM,KAAK,MAAM,UAAU;AAAA,YAC7B;AAAA,YACA,KAAK;AAAA,UACP;AACF,cAAM,KAAK,IAAI;AACf,eAAO,KAAK,MAAM;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,SAAS;AACtC,gBAAM,MAAM,CAAC,KAAK,MAAM,MAAM,CAAC,KAAK,KAAK,KAAK,MAAM;AAAA,MACxD;AACA,cAAQ,KAAK,SAAU,KAAK,KAAK,CAAC;AAAA,IACpC;AACA,WAAO;AACP,YAAQ;AAAA,EACV;AACA,MAAI,UAAU,WAAW;AACvB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,GAAG,IAAI,GAAG,MAAM,WAAW,OAAO,KAAK;AACpD,YAAM,QAAQ,CAAC,GAAG,SAAS,KAAK,IAAI,MAAM;AAC1C,eAAS,IAAI,GAAG,IAAI,OAAO,YAAY,KAAK;AAC1C,YAAI,OAAO,OAAO,MAAM,CAAC;AACzB,YAAI,MAAM,KAAK,MAAM,UAAU;AAC7B,iBAAO,KAAK,KAAK;AAAA,YACf;AAAA,cACE,GAAG,KAAK;AAAA,cACR,SAAS,KAAK,IAAI,GAAG,YAAY,KAAK,MAAM,OAAO;AAAA,YACrD;AAAA,YACA,KAAK;AAAA,UACP;AACF,cAAM,KAAK,IAAI;AAAA,MACjB;AACA,cAAQ,KAAK,SAAU,KAAK,KAAK,CAAC;AAAA,IACpC;AACA,WAAO;AACP,aAAS;AAAA,EACX;AACA,SAAO,EAAE,OAAO,QAAQ,KAAK;AAC/B;AACA,SAAS,UAAU,IAAI,KAAK,OAAO,OAAO,OAAO,QAAQ,SAAS;AAChE,QAAM,SAAS,GAAG,IAAI,KAAK;AAC3B,QAAM,QAAQ,eAAe,MAAM;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,IAAI,OAAO;AACrB,aAAS,MAAM,GAAG,SAAS,GAAG,MAAM,IAAI,QAAQ,OAAO;AACrD,YAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,gBAAU,QAAQ;AAClB,YAAM,QAAQ,CAAC;AACf,UAAI;AACJ,UAAI,QAAQ,aAAa,QAAQ,QAAQ,UAAU,QAAQ,MAAM;AAC/D,cAAM,UAAU,QAAQ,MAAM,KAAK,cAAc;AAAA,UAC9C,OAAM,cAAc,YAAY,MAAM,YAAY,cAAc;AACrE,eAAS,IAAI,IAAI,OAAO,IAAI,OAAO,IAAK,OAAM,KAAK,GAAG;AACtD,SAAG,OAAO,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,SAAS,IAAI,KAAK,GAAG,KAAK;AAAA,IACpE;AAAA,EACF;AACA,MAAI,SAAS,IAAI,QAAQ;AACvB,UAAM,QAAQ,CAAC;AACf,aAAS,IAAI,GAAG,UAAU,IAAI,SAAS,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,GAAG,KAAK;AAC1F,YAAM,SAAS,KAAK,IAAI,QAAQ,QAAQ,MAAM,OAAO,IAAI,IAAI,SAAS,CAAC,CAAC,EAAE,QAAQ,MAAM;AACxF,YAAM;AAAA,QACJ,SAAS,cAAc,YAAY,MAAM,YAAY,cAAc,KAAK,UAAU,QAAQ,MAAM,KAAK,cAAc;AAAA,MACrH;AAAA,IACF;AACA,UAAM,WAAW,MAAM,IAAI,OAAO,MAAM,SAAU,KAAK,KAAK,CAAC,GAAG,OAAO,CAAC;AACxE,aAAS,IAAI,IAAI,QAAQ,IAAI,QAAQ,IAAK,MAAK,KAAK,QAAQ;AAC5D,OAAG,OAAO,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,QAAQ,MAAM,WAAW,CAAC,GAAG,IAAI;AAAA,EAC3E;AACA,SAAO,CAAC,EAAE,SAAS;AACrB;AACA,SAAS,kBAAkB,IAAI,KAAK,OAAO,OAAO,MAAM,OAAO,KAAK,SAAS;AAC3E,MAAI,OAAO,KAAK,OAAO,IAAI,OAAQ,QAAO;AAC1C,MAAI,QAAQ;AACZ,WAAS,MAAM,MAAM,MAAM,OAAO,OAAO;AACvC,UAAM,QAAQ,MAAM,IAAI,QAAQ,KAAK,MAAM,IAAI,IAAI,KAAK;AACxD,QAAI,IAAI,IAAI,QAAQ,IAAI,KAAK,KAAK,KAAK;AACrC,cAAQ;AACR,YAAM,OAAO,MAAM,OAAO,GAAG;AAC7B,YAAM,EAAE,KAAK,SAAS,MAAM,SAAS,IAAI,IAAI,SAAS,GAAG;AACzD,SAAG,cAAc,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,MAAM,KAAK,GAAG,MAAM;AAAA,QACjE,GAAG,KAAK;AAAA,QACR,SAAS,MAAM;AAAA,MACjB,CAAC;AACD,SAAG;AAAA,QACD,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,IAAI,WAAW,KAAK,UAAU,KAAK,CAAC;AAAA,QAClE,KAAK,KAAK,cAAc;AAAA,UACtB,GAAG,KAAK;AAAA,UACR,SAAS,UAAU,KAAK,MAAM,UAAU;AAAA,QAC1C,CAAC;AAAA,MACH;AACA,aAAO,KAAK,MAAM,UAAU;AAAA,IAC9B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,IAAI,KAAK,OAAO,OAAO,KAAK,QAAQ,MAAM,SAAS;AAC1E,MAAI,QAAQ,KAAK,QAAQ,IAAI,MAAO,QAAO;AAC3C,MAAI,QAAQ;AACZ,WAAS,MAAM,KAAK,MAAM,QAAQ,OAAO;AACvC,UAAM,QAAQ,MAAM,IAAI,QAAQ,MAAM,MAAM,IAAI,IAAI,KAAK;AACzD,QAAI,IAAI,IAAI,QAAQ,CAAC,KAAK,KAAK;AAC7B,cAAQ;AACR,YAAM,OAAO,MAAM,OAAO,GAAG;AAC7B,YAAM,WAAW,IAAI,SAAS,GAAG;AACjC,YAAM,YAAY,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,MAAM,KAAK;AAC3D,SAAG;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,UACE,KAAK;AAAA,UACL,OAAO;AAAA,UACP,KAAK,MAAM,WAAW,OAAO;AAAA,QAC/B;AAAA,MACF;AACA,SAAG;AAAA,QACD,YAAY,KAAK;AAAA,QACjB,KAAK,KAAK;AAAA,UACR,cAAc,KAAK,OAAO,GAAG,OAAO,QAAQ;AAAA,QAC9C;AAAA,MACF;AACA,aAAO,KAAK,MAAM,UAAU;AAAA,IAC9B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,OAAO,UAAU,YAAY,MAAM,OAAO;AAC7D,MAAI,QAAQ,aAAa,MAAM,IAAI,OAAO,aAAa,CAAC,IAAI,MAAM;AAClE,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,gBAAgB;AAAA,EAClC;AACA,MAAI,MAAM,SAAS,IAAI,KAAK;AAC5B,QAAM,EAAE,KAAK,KAAK,IAAI;AACtB,QAAM,QAAQ,OAAO,MAAM,OAAO,SAAS,MAAM,MAAM;AACvD,QAAM,KAAK,MAAM;AACjB,MAAI,UAAU;AACd,WAAS,SAAS;AAChB,YAAQ,aAAa,GAAG,IAAI,OAAO,aAAa,CAAC,IAAI,GAAG;AACxD,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MAAM,gBAAgB;AAAA,IAClC;AACA,UAAM,SAAS,IAAI,KAAK;AACxB,cAAU,GAAG,QAAQ,KAAK;AAAA,EAC5B;AACA,MAAI,UAAU,IAAI,KAAK,OAAO,YAAY,OAAO,QAAQ,OAAO,EAAG,QAAO;AAC1E,MAAI,kBAAkB,IAAI,KAAK,OAAO,YAAY,MAAM,OAAO,KAAK,OAAO;AACzE,WAAO;AACT,MAAI,kBAAkB,IAAI,KAAK,OAAO,YAAY,MAAM,OAAO,QAAQ,OAAO;AAC5E,WAAO;AACT,MAAI,gBAAgB,IAAI,KAAK,OAAO,YAAY,KAAK,QAAQ,MAAM,OAAO;AACxE,WAAO;AACT,MAAI,gBAAgB,IAAI,KAAK,OAAO,YAAY,KAAK,QAAQ,OAAO,OAAO;AACzE,WAAO;AACT,WAAS,MAAM,KAAK,MAAM,QAAQ,OAAO;AACvC,UAAM,OAAO,IAAI,WAAW,KAAK,MAAM,KAAK,GAAG,KAAK,IAAI,WAAW,KAAK,OAAO,KAAK;AACpF,OAAG;AAAA,MACD,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,OAAO,UAAU;AAAA,MAC/C,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,KAAK,UAAU;AAAA,MAC7C,IAAI,MAAO,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC;AAAA,IACxC;AAAA,EACF;AACA,SAAO;AACP,KAAG;AAAA,IACD,IAAI;AAAA,MACF,GAAG,IAAI,QAAQ,aAAa,IAAI,WAAW,KAAK,MAAM,KAAK,CAAC;AAAA,MAC5D,GAAG,IAAI,QAAQ,aAAa,IAAI,WAAW,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;AAAA,IAC1E;AAAA,EACF;AACA,WAAS,EAAE;AACb;AAGA,IAAI,gBAAgB,eAAe;AAAA,EACjC,WAAW,MAAM,SAAS,EAAE;AAAA,EAC5B,YAAY,MAAM,SAAS,CAAC;AAAA,EAC5B,SAAS,MAAM,QAAQ,EAAE;AAAA,EACzB,WAAW,MAAM,QAAQ,CAAC;AAAA,EAC1B,mBAAmB,WAAW,SAAS,EAAE;AAAA,EACzC,oBAAoB,WAAW,SAAS,CAAC;AAAA,EACzC,iBAAiB,WAAW,QAAQ,EAAE;AAAA,EACtC,mBAAmB,WAAW,QAAQ,CAAC;AAAA,EACvC,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,cAAc;AAChB,CAAC;AACD,SAAS,kBAAkB,OAAO,UAAU,WAAW;AACrD,MAAI,UAAU,GAAG,MAAM,SAAS,EAAG,QAAO;AAC1C,MAAI,SAAU,UAAS,MAAM,GAAG,aAAa,SAAS,EAAE,eAAe,CAAC;AACxE,SAAO;AACT;AACA,SAAS,MAAM,MAAM,KAAK;AACxB,SAAO,CAAC,OAAO,UAAU,SAAS;AAChC,QAAI,CAAC,KAAM,QAAO;AAClB,UAAM,MAAM,MAAM;AAClB,QAAI,eAAe,eAAe;AAChC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,UAAW,KAAK,IAAI,WAAW,GAAG;AAAA,MACpC;AAAA,IACF;AACA,QAAI,QAAQ,WAAW,CAAC,IAAI,MAAO,QAAO;AAC1C,UAAM,MAAM,YAAY,MAAM,MAAM,GAAG;AACvC,QAAI,OAAO,KAAM,QAAO;AACxB,QAAI,QAAQ,SAAS;AACnB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,UAAW,KAAK,MAAM,IAAI,QAAQ,IAAI,OAAO,GAAG,GAAG,GAAG;AAAA,MACxD;AAAA,IACF,OAAO;AACL,YAAM,QAAQ,MAAM,IAAI,QAAQ,GAAG;AACnC,YAAM,QAAQ,SAAS,OAAO,MAAM,GAAG;AACvC,UAAI;AACJ,UAAI,MAAO,UAAS,UAAW,KAAK,OAAO,CAAC;AAAA,eACnC,MAAM;AACb,iBAAS,UAAW,KAAK,MAAM,IAAI,QAAQ,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE;AAAA,UAC7D,UAAS,UAAW,KAAK,MAAM,IAAI,QAAQ,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC;AACnE,aAAO,kBAAkB,OAAO,UAAU,MAAM;AAAA,IAClD;AAAA,EACF;AACF;AACA,SAAS,WAAW,MAAM,KAAK;AAC7B,SAAO,CAAC,OAAO,UAAU,SAAS;AAChC,QAAI,CAAC,KAAM,QAAO;AAClB,UAAM,MAAM,MAAM;AAClB,QAAI;AACJ,QAAI,eAAe,eAAe;AAChC,gBAAU;AAAA,IACZ,OAAO;AACL,YAAM,MAAM,YAAY,MAAM,MAAM,GAAG;AACvC,UAAI,OAAO,KAAM,QAAO;AACxB,gBAAU,IAAI,cAAc,MAAM,IAAI,QAAQ,GAAG,CAAC;AAAA,IACpD;AACA,UAAM,QAAQ,SAAS,QAAQ,WAAW,MAAM,GAAG;AACnD,QAAI,CAAC,MAAO,QAAO;AACnB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,IAAI,cAAc,QAAQ,aAAa,KAAK;AAAA,IAC9C;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,MAAM,KAAK;AACpC,QAAM,MAAM,KAAK,MAAM,KAAK,QAAQ,WAAW,IAAI,QAAQ,GAAG,CAAC;AAC/D,MAAI,CAAC,MAAO,QAAO;AACnB,OAAK,SAAS,KAAK,MAAM,GAAG,aAAa,IAAI,cAAc,KAAK,CAAC,CAAC;AAClE,SAAO;AACT;AACA,SAAS,YAAY,MAAM,GAAG,OAAO;AACnC,MAAI,CAAC,UAAU,KAAK,KAAK,EAAG,QAAO;AACnC,MAAI,QAAQ,YAAY,KAAK;AAC7B,QAAM,MAAM,KAAK,MAAM;AACvB,MAAI,eAAe,eAAe;AAChC,QAAI,CAAC;AACH,cAAQ;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,SAAU;AAAA,YACR,SAAS,eAAe,KAAK,MAAM,MAAM,EAAE,MAAM,KAAK;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AACF,UAAM,QAAQ,IAAI,YAAY,KAAK,EAAE;AACrC,UAAM,QAAQ,IAAI,YAAY,MAAM,EAAE;AACtC,UAAM,OAAO,SAAS,IAAI,KAAK,EAAE;AAAA,MAC/B,IAAI,YAAY,MAAM;AAAA,MACtB,IAAI,UAAU,MAAM;AAAA,IACtB;AACA,YAAQ,UAAU,OAAO,KAAK,QAAQ,KAAK,MAAM,KAAK,SAAS,KAAK,GAAG;AACvE,gBAAY,KAAK,OAAO,KAAK,UAAU,OAAO,MAAM,KAAK;AACzD,WAAO;AAAA,EACT,WAAW,OAAO;AAChB,UAAM,QAAQ,cAAc,KAAK,KAAK;AACtC,UAAM,QAAQ,MAAM,MAAM,EAAE;AAC5B;AAAA,MACE,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,SAAS,IAAI,MAAM,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,MAAM,KAAK;AAAA,MACvD;AAAA,IACF;AACA,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,gBAAgB,MAAM,YAAY;AACzC,MAAI;AACJ,MAAI,WAAW,WAAW,WAAW,QAAS;AAC9C,QAAM,eAAe,UAAU,MAAM,WAAW,MAAM;AACtD,MAAI;AACJ,MAAI,WAAW,YAAY,KAAK,MAAM,qBAAqB,eAAe;AACxE,qBAAiB,KAAK,MAAM,UAAU,aAAa,UAAU;AAC7D,eAAW,eAAe;AAAA,EAC5B,WAAW,WAAW,YAAY,iBAAiB,UAAU,WAAW,KAAK,MAAM,UAAU,OAAO,MAAM,UAAU,KAAK,eAAe,MAAM,UAAU,MAAM,OAAO,SAAS,GAAG,QAAQ,QAAQ,KAAK;AACpM,qBAAiB,SAAS,UAAU;AACpC,eAAW,eAAe;AAAA,EAC5B,WAAW,CAAC,cAAc;AACxB;AAAA,EACF;AACA,WAAS,iBAAiB,UAAU,OAAO;AACzC,QAAI,QAAQ,eAAe,MAAM,KAAK;AACtC,UAAM,WAAW,gBAAgB,SAAS,KAAK,KAAK,KAAK;AACzD,QAAI,CAAC,SAAS,CAAC,YAAY,UAAU,KAAK,GAAG;AAC3C,UAAI,SAAU,SAAQ;AAAA,UACjB;AAAA,IACP;AACA,UAAM,YAAY,IAAI,cAAc,UAAU,KAAK;AACnD,QAAI,YAAY,CAAC,KAAK,MAAM,UAAU,GAAG,SAAS,GAAG;AACnD,YAAM,KAAK,KAAK,MAAM,GAAG,aAAa,SAAS;AAC/C,UAAI,SAAU,IAAG,QAAQ,iBAAiB,SAAS,GAAG;AACtD,WAAK,SAAS,EAAE;AAAA,IAClB;AAAA,EACF;AACA,WAAS,OAAO;AACd,SAAK,KAAK,oBAAoB,WAAW,IAAI;AAC7C,SAAK,KAAK,oBAAoB,aAAa,IAAI;AAC/C,SAAK,KAAK,oBAAoB,aAAa,IAAI;AAC/C,QAAI,gBAAgB,SAAS,KAAK,KAAK,KAAK;AAC1C,WAAK,SAAS,KAAK,MAAM,GAAG,QAAQ,iBAAiB,EAAE,CAAC;AAAA,EAC5D;AACA,WAAS,KAAK,QAAQ;AACpB,UAAM,QAAQ;AACd,UAAM,SAAS,gBAAgB,SAAS,KAAK,KAAK;AAClD,QAAI;AACJ,QAAI,UAAU,MAAM;AAClB,iBAAW,KAAK,MAAM,IAAI,QAAQ,MAAM;AAAA,IAC1C,WAAW,UAAU,MAAM,MAAM,MAAM,KAAK,cAAc;AACxD,iBAAW,eAAe,MAAM,UAAU;AAC1C,UAAI,CAAC,SAAU,QAAO,KAAK;AAAA,IAC7B;AACA,QAAI,SAAU,kBAAiB,UAAU,KAAK;AAAA,EAChD;AACA,OAAK,KAAK,iBAAiB,WAAW,IAAI;AAC1C,OAAK,KAAK,iBAAiB,aAAa,IAAI;AAC5C,OAAK,KAAK,iBAAiB,aAAa,IAAI;AAC9C;AACA,SAAS,YAAY,MAAM,MAAM,KAAK;AACpC,MAAI,EAAE,KAAK,MAAM,qBAAqB,eAAiB,QAAO;AAC9D,QAAM,EAAE,MAAM,IAAI,KAAK,MAAM;AAC7B,WAAS,IAAI,MAAM,QAAQ,GAAG,KAAK,GAAG,KAAK;AACzC,UAAM,SAAS,MAAM,KAAK,CAAC,GAAG,QAAQ,MAAM,IAAI,MAAM,MAAM,CAAC,IAAI,MAAM,WAAW,CAAC;AACnF,QAAI,UAAU,MAAM,IAAI,IAAI,OAAO,YAAa,QAAO;AACvD,QAAI,OAAO,KAAK,KAAK,aAAa,UAAU,OAAO,KAAK,KAAK,aAAa,eAAe;AACvF,YAAM,UAAU,MAAM,OAAO,CAAC;AAC9B,YAAM,SAAS,QAAQ,SAAS,MAAM,IAAI,SAAS,OAAO,MAAM,IAAI,UAAU;AAC9E,aAAO,KAAK,eAAe,MAAM,IAAI,UAAU;AAAA,IACjD;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,UAAU,MAAM,KAAK;AAC5B,SAAO,OAAO,OAAO,KAAK,KAAK,MAAM,IAAI,YAAY;AACnD,QAAI,IAAI,YAAY,QAAQ,IAAI,YAAY,MAAM;AAChD,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,MAAM,OAAO;AACnC,QAAM,WAAW,KAAK,YAAY;AAAA,IAChC,MAAM,MAAM;AAAA,IACZ,KAAK,MAAM;AAAA,EACb,CAAC;AACD,MAAI,CAAC,SAAU,QAAO;AACtB,SAAO,WAAW,WAAW,KAAK,MAAM,IAAI,QAAQ,SAAS,GAAG,CAAC,IAAI;AACvE;AAUA,IAAI,YAAY,MAAM;AAAA,EACpB,YAAY,MAAM,qBAAqB;AACrC,SAAK,OAAO;AACZ,SAAK,sBAAsB;AAC3B,SAAK,MAAM,SAAS,cAAc,KAAK;AACvC,SAAK,IAAI,YAAY;AACrB,SAAK,QAAQ,KAAK,IAAI,YAAY,SAAS,cAAc,OAAO,CAAC;AACjE,SAAK,MAAM,MAAM;AAAA,MACf;AAAA,MACA,GAAG,mBAAmB;AAAA,IACxB;AACA,SAAK,WAAW,KAAK,MAAM,YAAY,SAAS,cAAc,UAAU,CAAC;AACzE,0BAAsB,MAAM,KAAK,UAAU,KAAK,OAAO,mBAAmB;AAC1E,SAAK,aAAa,KAAK,MAAM,YAAY,SAAS,cAAc,OAAO,CAAC;AAAA,EAC1E;AAAA,EACA,OAAO,MAAM;AACX,QAAI,KAAK,QAAQ,KAAK,KAAK,KAAM,QAAO;AACxC,SAAK,OAAO;AACZ;AAAA,MACE;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,QAAQ;AACrB,WAAO,OAAO,QAAQ,iBAAiB,OAAO,UAAU,KAAK,SAAS,KAAK,SAAS,SAAS,OAAO,MAAM;AAAA,EAC5G;AACF;AACA,SAAS,sBAAsB,MAAM,UAAU,OAAO,qBAAqB,aAAa,eAAe;AACrG,MAAI;AACJ,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,MAAI,UAAU,SAAS;AACvB,QAAM,MAAM,KAAK;AACjB,MAAI,CAAC,IAAK;AACV,WAAS,IAAI,GAAG,MAAM,GAAG,IAAI,IAAI,YAAY,KAAK;AAChD,UAAM,EAAE,SAAS,SAAS,IAAI,IAAI,MAAM,CAAC,EAAE;AAC3C,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK,OAAO;AACvC,YAAM,WAAW,eAAe,MAAM,gBAAgB,YAAY,SAAS,CAAC;AAC5E,YAAM,WAAW,WAAW,WAAW,OAAO;AAC9C,oBAAc,YAAY;AAC1B,UAAI,CAAC,SAAU,cAAa;AAC5B,UAAI,CAAC,SAAS;AACZ,cAAM,OAAO,SAAS,cAAc,KAAK;AACzC,aAAK,MAAM,QAAQ;AACnB,iBAAS,YAAY,IAAI;AAAA,MAC3B,OAAO;AACL,YAAI,QAAQ,MAAM,SAAS,UAAU;AACnC,kBAAQ,MAAM,QAAQ;AAAA,QACxB;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAS;AACd,UAAM,QAAQ,QAAQ;AACtB,KAAC,KAAK,QAAQ,eAAe,OAAO,SAAS,GAAG,YAAY,OAAO;AACnE,cAAU;AAAA,EACZ;AACA,MAAI,YAAY;AACd,UAAM,MAAM,QAAQ,aAAa;AACjC,UAAM,MAAM,WAAW;AAAA,EACzB,OAAO;AACL,UAAM,MAAM,QAAQ;AACpB,UAAM,MAAM,WAAW,aAAa;AAAA,EACtC;AACF;AAGA,IAAI,0BAA0B,IAAI;AAAA,EAChC;AACF;AACA,SAAS,eAAe;AAAA,EACtB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,OAAO;AAAA,EACP,sBAAsB;AACxB,IAAI,CAAC,GAAG;AACN,QAAM,SAAS,IAAI,OAAO;AAAA,IACxB,KAAK;AAAA,IACL,OAAO;AAAA,MACL,KAAK,GAAG,OAAO;AACb,YAAI,IAAI;AACR,cAAM,aAAa,MAAM,KAAK,OAAO,SAAS,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG;AAC9F,cAAM,YAAY,eAAe,MAAM,MAAM,EAAE,MAAM;AACrD,YAAI,QAAQ,WAAW;AACrB,oBAAU,SAAS,IAAI,CAAC,MAAM,SAAS;AACrC,mBAAO,IAAI,KAAK,MAAM,qBAAqB,IAAI;AAAA,UACjD;AAAA,QACF;AACA,eAAO,IAAI,YAAY,IAAI,KAAK;AAAA,MAClC;AAAA,MACA,MAAM,IAAI,MAAM;AACd,eAAO,KAAK,MAAM,EAAE;AAAA,MACtB;AAAA,IACF;AAAA,IACA,OAAO;AAAA,MACL,YAAY,CAAC,UAAU;AACrB,cAAM,cAAc,wBAAwB,SAAS,KAAK;AAC1D,eAAO,eAAe,YAAY,eAAe,KAAK,EAAE,OAAO,gBAAgB,IAAI,CAAC;AAAA,MACtF;AAAA,MACA,iBAAiB;AAAA,QACf,WAAW,CAAC,MAAM,UAAU;AAC1B,0BAAgB,MAAM,OAAO,aAAa,mBAAmB;AAAA,QAC/D;AAAA,QACA,YAAY,CAAC,SAAS;AACpB,2BAAiB,IAAI;AAAA,QACvB;AAAA,QACA,WAAW,CAAC,MAAM,UAAU;AAC1B,2BAAiB,MAAM,OAAO,cAAc,mBAAmB;AAAA,QACjE;AAAA,MACF;AAAA,MACA,aAAa,CAAC,UAAU;AACtB,cAAM,cAAc,wBAAwB,SAAS,KAAK;AAC1D,YAAI,eAAe,YAAY,eAAe,IAAI;AAChD,iBAAO,kBAAkB,OAAO,YAAY,YAAY;AAAA,QAC1D;AAAA,MACF;AAAA,MACA,WAAW,CAAC;AAAA,IACd;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAI,cAAc,MAAM,aAAa;AAAA,EACnC,YAAY,cAAc,UAAU;AAClC,SAAK,eAAe;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,MAAM,IAAI;AACR,UAAM,QAAQ;AACd,UAAM,SAAS,GAAG,QAAQ,uBAAuB;AACjD,QAAI,UAAU,OAAO,aAAa;AAChC,aAAO,IAAI,aAAa,OAAO,WAAW,KAAK;AACjD,QAAI,UAAU,OAAO,gBAAgB;AACnC,aAAO,IAAI,aAAa,MAAM,cAAc,OAAO,WAAW;AAChE,QAAI,MAAM,eAAe,MAAM,GAAG,YAAY;AAC5C,UAAI,SAAS,GAAG,QAAQ,IAAI,MAAM,cAAc,EAAE;AAClD,UAAI,CAAC,aAAa,GAAG,IAAI,QAAQ,MAAM,CAAC,GAAG;AACzC,iBAAS;AAAA,MACX;AACA,aAAO,IAAI,aAAa,QAAQ,MAAM,QAAQ;AAAA,IAChD;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,gBAAgB,MAAM,OAAO,aAAa,qBAAqB;AACtE,MAAI,CAAC,KAAK,SAAU;AACpB,QAAM,cAAc,wBAAwB,SAAS,KAAK,KAAK;AAC/D,MAAI,CAAC,YAAa;AAClB,MAAI,CAAC,YAAY,UAAU;AACzB,UAAM,SAAS,cAAc,MAAM,MAAM;AACzC,QAAI,OAAO;AACX,QAAI,QAAQ;AACV,YAAM,EAAE,MAAM,MAAM,IAAI,OAAO,sBAAsB;AACrD,UAAI,MAAM,UAAU,QAAQ;AAC1B,eAAO,SAAS,MAAM,OAAO,QAAQ,WAAW;AAAA,eACzC,QAAQ,MAAM,WAAW;AAChC,eAAO,SAAS,MAAM,OAAO,SAAS,WAAW;AAAA,IACrD;AACA,QAAI,QAAQ,YAAY,cAAc;AACpC,UAAI,CAAC,uBAAuB,SAAS,IAAI;AACvC,cAAM,QAAQ,KAAK,MAAM,IAAI,QAAQ,IAAI;AACzC,cAAM,QAAQ,MAAM,KAAK,EAAE;AAC3B,cAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,cAAM,aAAa,MAAM,MAAM,EAAE;AACjC,cAAM,MAAM,IAAI,SAAS,MAAM,MAAM,UAAU,IAAI,MAAM,UAAU,MAAM,UAAU;AACnF,YAAI,OAAO,IAAI,QAAQ,GAAG;AACxB;AAAA,QACF;AAAA,MACF;AACA,mBAAa,MAAM,IAAI;AAAA,IACzB;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,CAAC,KAAK,SAAU;AACpB,QAAM,cAAc,wBAAwB,SAAS,KAAK,KAAK;AAC/D,MAAI,eAAe,YAAY,eAAe,MAAM,CAAC,YAAY;AAC/D,iBAAa,MAAM,EAAE;AACzB;AACA,SAAS,iBAAiB,MAAM,OAAO,cAAc,qBAAqB;AACxE,MAAI;AACJ,MAAI,CAAC,KAAK,SAAU,QAAO;AAC3B,QAAM,OAAO,KAAK,KAAK,IAAI,cAAc,gBAAgB,OAAO,KAAK;AACrE,QAAM,cAAc,wBAAwB,SAAS,KAAK,KAAK;AAC/D,MAAI,CAAC,eAAe,YAAY,gBAAgB,MAAM,YAAY;AAChE,WAAO;AACT,QAAM,OAAO,KAAK,MAAM,IAAI,OAAO,YAAY,YAAY;AAC3D,QAAM,QAAQ,gBAAgB,MAAM,YAAY,cAAc,KAAK,KAAK;AACxE,OAAK;AAAA,IACH,KAAK,MAAM,GAAG,QAAQ,yBAAyB;AAAA,MAC7C,aAAa,EAAE,QAAQ,MAAM,SAAS,YAAY,MAAM;AAAA,IAC1D,CAAC;AAAA,EACH;AACA,WAAS,OAAO,QAAQ;AACtB,QAAI,oBAAoB,WAAW,MAAM;AACzC,QAAI,oBAAoB,aAAa,IAAI;AACzC,UAAM,eAAe,wBAAwB,SAAS,KAAK,KAAK;AAChE,QAAI,gBAAgB,OAAO,SAAS,aAAa,UAAU;AACzD;AAAA,QACE;AAAA,QACA,aAAa;AAAA,QACb,aAAa,aAAa,UAAU,QAAQ,YAAY;AAAA,MAC1D;AACA,WAAK;AAAA,QACH,KAAK,MAAM,GAAG,QAAQ,yBAAyB,EAAE,aAAa,KAAK,CAAC;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AACA,WAAS,KAAK,QAAQ;AACpB,QAAI,CAAC,OAAO,MAAO,QAAO,OAAO,MAAM;AACvC,UAAM,eAAe,wBAAwB,SAAS,KAAK,KAAK;AAChE,QAAI,CAAC,aAAc;AACnB,QAAI,aAAa,UAAU;AACzB,YAAM,UAAU,aAAa,aAAa,UAAU,QAAQ,YAAY;AACxE;AAAA,QACE;AAAA,QACA,aAAa;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACA,MAAI,iBAAiB,WAAW,MAAM;AACtC,MAAI,iBAAiB,aAAa,IAAI;AACtC,QAAM,eAAe;AACrB,SAAO;AACT;AACA,SAAS,gBAAgB,MAAM,SAAS,EAAE,SAAS,SAAS,GAAG;AAC7D,QAAM,QAAQ,YAAY,SAAS,SAAS,SAAS,CAAC;AACtD,MAAI,MAAO,QAAO;AAClB,QAAM,MAAM,KAAK,SAAS,OAAO;AACjC,QAAM,OAAO,IAAI,KAAK,WAAW,IAAI,MAAM;AAC3C,MAAI,WAAW,KAAK,aAAa,QAAQ;AACzC,MAAI,UAAU;AACZ,aAAS,IAAI,GAAG,IAAI,SAAS;AAC3B,UAAI,SAAS,CAAC,GAAG;AACf,oBAAY,SAAS,CAAC;AACtB;AAAA,MACF;AAAA,EACJ;AACA,SAAO,WAAW;AACpB;AACA,SAAS,cAAc,QAAQ;AAC7B,SAAO,UAAU,OAAO,YAAY,QAAQ,OAAO,YAAY;AAC7D,aAAS,OAAO,aAAa,OAAO,UAAU,SAAS,aAAa,IAAI,OAAO,OAAO;AACxF,SAAO;AACT;AACA,SAAS,SAAS,MAAM,OAAO,MAAM,aAAa;AAChD,QAAM,SAAS,QAAQ,UAAU,CAAC,cAAc;AAChD,QAAM,QAAQ,KAAK,YAAY;AAAA,IAC7B,MAAM,MAAM,UAAU;AAAA,IACtB,KAAK,MAAM;AAAA,EACb,CAAC;AACD,MAAI,CAAC,MAAO,QAAO;AACnB,QAAM,EAAE,IAAI,IAAI;AAChB,QAAM,QAAQ,WAAW,KAAK,MAAM,IAAI,QAAQ,GAAG,CAAC;AACpD,MAAI,CAAC,MAAO,QAAO;AACnB,MAAI,QAAQ,QAAS,QAAO,MAAM;AAClC,QAAM,MAAM,SAAS,IAAI,MAAM,KAAK,EAAE,CAAC,GAAG,QAAQ,MAAM,MAAM,EAAE;AAChE,QAAM,QAAQ,IAAI,IAAI,QAAQ,MAAM,MAAM,KAAK;AAC/C,SAAO,QAAQ,IAAI,SAAS,IAAI,KAAK,QAAQ,IAAI,IAAI,QAAQ,CAAC;AAChE;AACA,SAAS,aAAa,UAAU,OAAO,gBAAgB;AACrD,QAAM,SAAS,MAAM,UAAU,SAAS;AACxC,SAAO,KAAK,IAAI,gBAAgB,SAAS,aAAa,MAAM;AAC9D;AACA,SAAS,aAAa,MAAM,OAAO;AACjC,OAAK;AAAA,IACH,KAAK,MAAM,GAAG,QAAQ,yBAAyB,EAAE,WAAW,MAAM,CAAC;AAAA,EACrE;AACF;AACA,SAAS,kBAAkB,MAAM,MAAM,OAAO;AAC5C,QAAM,QAAQ,KAAK,MAAM,IAAI,QAAQ,IAAI;AACzC,QAAM,QAAQ,MAAM,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,KAAK,GAAG,QAAQ,MAAM,MAAM,EAAE;AAC/E,QAAM,MAAM,IAAI,SAAS,MAAM,MAAM,KAAK,IAAI,MAAM,UAAU,MAAM,UAAU;AAC9E,QAAM,KAAK,KAAK,MAAM;AACtB,WAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,OAAO;AACzC,UAAM,WAAW,MAAM,IAAI,QAAQ;AACnC,QAAI,OAAO,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,WAAW,IAAI,KAAK,EAAG;AAC/D,UAAM,MAAM,IAAI,IAAI,QAAQ;AAC5B,UAAM,QAAQ,MAAM,OAAO,GAAG,EAAE;AAChC,UAAM,QAAQ,MAAM,WAAW,IAAI,IAAI,MAAM,IAAI,SAAS,GAAG;AAC7D,QAAI,MAAM,YAAY,MAAM,SAAS,KAAK,KAAK,MAAO;AACtD,UAAM,WAAW,MAAM,WAAW,MAAM,SAAS,MAAM,IAAI,OAAO,MAAM,OAAO;AAC/E,aAAS,KAAK,IAAI;AAClB,OAAG,cAAc,QAAQ,KAAK,MAAM,EAAE,GAAG,OAAO,SAAS,CAAC;AAAA,EAC5D;AACA,MAAI,GAAG,WAAY,MAAK,SAAS,EAAE;AACrC;AACA,SAAS,mBAAmB,MAAM,MAAM,OAAO,qBAAqB;AAClE,QAAM,QAAQ,KAAK,MAAM,IAAI,QAAQ,IAAI;AACzC,QAAM,QAAQ,MAAM,KAAK,EAAE,GAAG,QAAQ,MAAM,MAAM,EAAE;AACpD,QAAM,MAAM,SAAS,IAAI,KAAK,EAAE,SAAS,MAAM,MAAM,KAAK,IAAI,MAAM,UAAU,MAAM,UAAU;AAC9F,MAAI,MAAM,KAAK,SAAS,MAAM,MAAM,EAAE,CAAC,EAAE;AACzC,SAAO,OAAO,IAAI,YAAY,SAAS;AACrC,UAAM,IAAI;AAAA,EACZ;AACA,MAAI,CAAC,IAAK;AACV;AAAA,IACE;AAAA,IACA,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,OAAO,GAAG;AACjB,SAAO,MAAM,CAAC,EAAE,KAAK,CAAC;AACxB;AACA,SAAS,kBAAkB,OAAO,MAAM;AACtC,MAAI;AACJ,QAAM,cAAc,CAAC;AACrB,QAAM,QAAQ,MAAM,IAAI,QAAQ,IAAI;AACpC,QAAM,QAAQ,MAAM,KAAK,EAAE;AAC3B,MAAI,CAAC,OAAO;AACV,WAAO,cAAe;AAAA,EACxB;AACA,QAAM,MAAM,SAAS,IAAI,KAAK;AAC9B,QAAM,QAAQ,MAAM,MAAM,EAAE;AAC5B,QAAM,MAAM,IAAI,SAAS,MAAM,MAAM,KAAK,IAAI,MAAM,UAAU,MAAM,UAAU;AAC9E,WAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,OAAO;AACzC,UAAM,QAAQ,MAAM,MAAM,IAAI;AAC9B,SAAK,OAAO,IAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,IAAI;AAChI,YAAM,UAAU,IAAI,IAAI,KAAK;AAC7B,YAAM,MAAM,QAAQ,UAAU,MAAM,OAAO,OAAO,EAAE,WAAW;AAC/D,YAAM,MAAM,SAAS,cAAc,KAAK;AACxC,UAAI,YAAY;AAChB,WAAK,KAAK,wBAAwB,SAAS,KAAK,MAAM,OAAO,SAAS,GAAG,UAAU;AACjF,oBAAY;AAAA,UACV,WAAY;AAAA,YACV,QAAQ;AAAA,YACR,QAAQ,UAAU,MAAM,OAAO,OAAO,EAAE;AAAA,YACxC;AAAA,cACE,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,kBAAY,KAAK,WAAY,OAAO,KAAK,GAAG,CAAC;AAAA,IAC/C;AAAA,EACF;AACA,SAAO,cAAe,OAAO,MAAM,KAAK,WAAW;AACrD;AAGA,SAAS,aAAa;AAAA,EACpB,0BAA0B;AAC5B,IAAI,CAAC,GAAG;AACN,SAAO,IAAI,OAAQ;AAAA,IACjB,KAAK;AAAA;AAAA;AAAA;AAAA,IAIL,OAAO;AAAA,MACL,OAAO;AACL,eAAO;AAAA,MACT;AAAA,MACA,MAAM,IAAI,KAAK;AACb,cAAM,MAAM,GAAG,QAAQ,eAAe;AACtC,YAAI,OAAO,KAAM,QAAO,OAAO,KAAK,OAAO;AAC3C,YAAI,OAAO,QAAQ,CAAC,GAAG,WAAY,QAAO;AAC1C,cAAM,EAAE,SAAS,IAAI,IAAI,GAAG,QAAQ,UAAU,GAAG;AACjD,eAAO,UAAU,OAAO;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,OAAO;AAAA,MACL,aAAa;AAAA,MACb,iBAAiB;AAAA,QACf,WAAW;AAAA,MACb;AAAA,MACA,uBAAuB,MAAM;AAC3B,eAAO,gBAAgB,SAAS,KAAK,KAAK,KAAK,OAAO,KAAK,MAAM,YAAY;AAAA,MAC/E;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,kBAAkB,GAAG,UAAU,OAAO;AACpC,aAAO;AAAA,QACL;AAAA,QACA,UAAU,OAAO,QAAQ;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AClwEgB,SAAA,uBAAuB,UAAkB,OAAyB;AAChF,MAAI,OAAO;AAET,WAAO,CAAC,SAAS,GAAG,KAAK,IAAI,OAAO,QAAQ,CAAC,IAAI;;AAInD,SAAO,CAAC,aAAa,GAAG,QAAQ,IAAI;AAEtC;SCJgB,cACd,MACA,UACA,OACA,cACA,aACA,eAAsB;;AAEtB,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,MAAI,UAAU,SAAS;AACvB,QAAM,MAAM,KAAK;AAEjB,MAAI,QAAQ,MAAM;AAChB,aAAS,IAAI,GAAG,MAAM,GAAG,IAAI,IAAI,YAAY,KAAK,GAAG;AACnD,YAAM,EAAE,SAAS,SAAQ,IAAK,IAAI,MAAM,CAAC,EAAE;AAE3C,eAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG,OAAO,GAAG;AAC7C,cAAM,WAAW,gBAAgB,MAAM,gBAAiB,YAAY,SAAS,CAAC;AAC9E,cAAM,WAAW,WAAW,GAAG,QAAQ,OAAO;AAE9C,sBAAc,YAAY;AAE1B,YAAI,CAAC,UAAU;AACb,uBAAa;;AAGf,YAAI,CAAC,SAAS;AACZ,gBAAM,aAAa,SAAS,cAAc,KAAK;AAE/C,gBAAM,CAAC,aAAa,aAAa,IAAI,uBAAuB,cAAc,QAAQ;AAElF,qBAAW,MAAM,YAAY,aAAa,aAAa;AAEvD,mBAAS,YAAY,UAAU;eAC1B;AACL,cAAK,QAAgC,MAAM,UAAU,UAAU;AAC7D,kBAAM,CAAC,aAAa,aAAa,IAAI,uBAAuB,cAAc,QAAQ;AAEjF,oBAAgC,MAAM,YAAY,aAAa,aAAa;;AAG/E,oBAAU,QAAQ;;;;;AAM1B,SAAO,SAAS;AACd,UAAM,QAAQ,QAAQ;AAEtB,KAAA,KAAA,QAAQ,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,YAAY,OAAO;AACvC,cAAU;;AAGZ,MAAI,YAAY;AACd,UAAM,MAAM,QAAQ,GAAG,UAAU;AACjC,UAAM,MAAM,WAAW;SAClB;AACL,UAAM,MAAM,QAAQ;AACpB,UAAM,MAAM,WAAW,GAAG,UAAU;;AAExC;IAEaA,mBAAS;EAapB,YAAY,MAAuB,cAAoB;AACrD,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,MAAM,SAAS,cAAc,KAAK;AACvC,SAAK,IAAI,YAAY;AACrB,SAAK,QAAQ,KAAK,IAAI,YAAY,SAAS,cAAc,OAAO,CAAC;AACjE,SAAK,WAAW,KAAK,MAAM,YAAY,SAAS,cAAc,UAAU,CAAC;AACzE,kBAAc,MAAM,KAAK,UAAU,KAAK,OAAO,YAAY;AAC3D,SAAK,aAAa,KAAK,MAAM,YAAY,SAAS,cAAc,OAAO,CAAC;;EAG1E,OAAO,MAAqB;AAC1B,QAAI,KAAK,SAAS,KAAK,KAAK,MAAM;AAChC,aAAO;;AAGT,SAAK,OAAO;AACZ,kBAAc,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,YAAY;AAEhE,WAAO;;EAGT,eAAe,UAA4B;AACzC,WACE,SAAS,SAAS,iBACd,SAAS,WAAW,KAAK,SAAS,KAAK,SAAS,SAAS,SAAS,MAAM;;AAGjF;ACjFK,SAAU,eACd,MACA,cACA,aACA,eAAsB;AAEtB,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,QAAM,OAAwB,CAAA;AAC9B,QAAM,MAAM,KAAK;AAEjB,MAAI,CAAC,KAAK;AACR,WAAO,CAAA;;AAGT,WAAS,IAAI,GAAG,MAAM,GAAG,IAAI,IAAI,YAAY,KAAK,GAAG;AACnD,UAAM,EAAE,SAAS,SAAQ,IAAK,IAAI,MAAM,CAAC,EAAE;AAE3C,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG,OAAO,GAAG;AAC7C,YAAM,WAAW,gBAAgB,MAAM,gBAAgB,YAAY,SAAS,CAAC;AAE7E,oBAAc,YAAY;AAE1B,UAAI,CAAC,UAAU;AACb,qBAAa;;AAGf,YAAM,CAAC,UAAU,KAAK,IAAI,uBAAuB,cAAc,QAAQ;AAEvE,WAAK,KAAK;QACR;QACA,EAAE,OAAO,GAAG,QAAQ,KAAK,KAAK,GAAE;MACjC,CAAA;;;AAIL,QAAM,aAAa,aAAa,GAAG,UAAU,OAAO;AACpD,QAAM,gBAAgB,aAAa,KAAK,GAAG,UAAU;AAErD,QAAM,WAA0B,CAAC,YAAY,CAAA,GAAI,GAAG,IAAI;AAExD,SAAO,EAAE,UAAU,YAAY,cAAa;AAC9C;ACrEgB,SAAA,WACd,UACA,aAAiE;AAEjE,MAAI,aAAa;AACf,WAAO,SAAS,cAAc,MAAM,WAAW;;AAGjD,SAAO,SAAS,cAAa;AAC/B;ACTM,SAAU,kBAAkB,QAAc;AAC9C,MAAI,OAAO,OAAO,gBAAgB;AAChC,WAAO,OAAO,OAAO;;AAGvB,QAAM,QAAqC,CAAA;AAE3C,SAAO,KAAK,OAAO,KAAK,EAAE,QAAQ,UAAO;AACvC,UAAM,WAAW,OAAO,MAAM,IAAI;AAElC,QAAI,SAAS,KAAK,WAAW;AAC3B,YAAM,SAAS,KAAK,SAAS,IAAI;;EAErC,CAAC;AAED,SAAO,OAAO,iBAAiB;AAE/B,SAAO;AACT;ACfM,SAAU,YACd,QACA,WACA,WACA,eACA,aAAiE;AAEjE,QAAM,QAAQ,kBAAkB,MAAM;AACtC,QAAM,cAAiC,CAAA;AACvC,QAAM,QAA2B,CAAA;AAEjC,WAAS,QAAQ,GAAG,QAAQ,WAAW,SAAS,GAAG;AACjD,UAAM,OAAO,WAAW,MAAM,MAAM,WAAW;AAE/C,QAAI,MAAM;AACR,YAAM,KAAK,IAAI;;AAGjB,QAAI,eAAe;AACjB,YAAM,aAAa,WAAW,MAAM,aAAa,WAAW;AAE5D,UAAI,YAAY;AACd,oBAAY,KAAK,UAAU;;;;AAKjC,QAAM,OAA0B,CAAA;AAEhC,WAAS,QAAQ,GAAG,QAAQ,WAAW,SAAS,GAAG;AACjD,SAAK,KAAK,MAAM,IAAI,cAAc,MAAM,iBAAiB,UAAU,IAAI,cAAc,KAAK,CAAC;;AAG7F,SAAO,MAAM,MAAM,cAAc,MAAM,IAAI;AAC7C;ACrCM,SAAU,gBAAgB,OAAc;AAC5C,SAAO,iBAAiB;AAC1B;ACAO,IAAM,kCAA2D,CAAC,EAAE,OAAM,MAAM;AACrF,QAAM,EAAE,UAAS,IAAK,OAAO;AAE7B,MAAI,CAAC,gBAAgB,SAAS,GAAG;AAC/B,WAAO;;AAGT,MAAI,YAAY;AAChB,QAAM,QAAQ,2BAA2B,UAAU,OAAO,CAAC,EAAE,OAAO,UAAO;AACzE,WAAO,KAAK,KAAK,SAAS;EAC5B,CAAC;AAED,YAAK,QAAL,UAAK,SAAA,SAAL,MAAO,KAAK,YAAY,UAAO;AAC7B,QAAI,KAAK,KAAK,SAAS,SAAS;AAC9B,aAAO;;AAGT,QAAI,CAAC,aAAa,aAAa,EAAE,SAAS,KAAK,KAAK,IAAI,GAAG;AACzD,mBAAa;;EAEjB,CAAC;AAED,QAAM,mBAAmB,cAAc,UAAU,OAAO;AAExD,MAAI,CAAC,kBAAkB;AACrB,WAAO;;AAGT,SAAO,SAAS,YAAW;AAE3B,SAAO;AACT;ACqNa,IAAA,QAAQ,KAAK,OAAqB;EAC7C,MAAM;;EAGN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;MAChB,WAAW;MACX,aAAa;MACb,cAAc;;MAEd,MAAMA;MACN,qBAAqB;MACrB,yBAAyB;;;EAI7B,SAAS;EAET,WAAW;EAEX,WAAW;EAEX,OAAO;EAEP,YAAS;AACP,WAAO,CAAC,EAAE,KAAK,QAAO,CAAE;;EAG1B,WAAW,EAAE,MAAM,eAAc,GAAE;AACjC,UAAM,EAAE,UAAU,YAAY,cAAa,IAAK,eAC9C,MACA,KAAK,QAAQ,YAAY;AAG3B,UAAM,QAAuB;MAC3B;MACA,gBAAgB,KAAK,QAAQ,gBAAgB,gBAAgB;QAC3D,OAAO,aACH,UAAU,UAAU,KACpB,cAAc,aAAa;OAChC;MACD;MACA,CAAC,SAAS,CAAC;;AAGb,WAAO;;EAGT,cAAW;AACT,WAAO;MACL,aACE,CAAC,EAAE,OAAO,GAAG,OAAO,GAAG,gBAAgB,KAAI,IAAK,CAAA,MAAO,CAAC,EAAE,IAAI,UAAU,OAAM,MAAM;AAClF,cAAM,OAAO,YAAY,OAAO,QAAQ,MAAM,MAAM,aAAa;AAEjE,YAAI,UAAU;AACZ,gBAAM,SAAS,GAAG,UAAU,OAAO;AAEnC,aAAG,qBAAqB,IAAI,EACzB,eAAc,EACd,aAAa,cAAc,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,CAAC;;AAG5D,eAAO;;MAEX,iBACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,gBAAgB,OAAO,QAAQ;;MAE1C,gBACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,eAAe,OAAO,QAAQ;;MAEzC,cACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,aAAa,OAAO,QAAQ;;MAEvC,cACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,aAAa,OAAO,QAAQ;;MAEvC,aACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,YAAY,OAAO,QAAQ;;MAEtC,WACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,UAAU,OAAO,QAAQ;;MAEpC,aACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,YAAY,OAAO,QAAQ;;MAEtC,YACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,WAAW,OAAO,QAAQ;;MAErC,WACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,UAAU,OAAO,QAAQ;;MAEpC,oBACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,aAAa,QAAQ,EAAE,OAAO,QAAQ;;MAEjD,iBACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,aAAa,KAAK,EAAE,OAAO,QAAQ;;MAE9C,kBACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,iBAAiB,OAAO,QAAQ;;MAE3C,cACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,YAAI,WAAW,OAAO,QAAQ,GAAG;AAC/B,iBAAO;;AAGT,eAAO,UAAU,OAAO,QAAQ;;MAEpC,kBACE,CAAC,MAAM,UAAU,CAAC,EAAE,OAAO,SAAQ,MAAM;AACvC,eAAO,YAAY,MAAM,KAAK,EAAE,OAAO,QAAQ;;MAEnD,cACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,aAAa,CAAC,EAAE,OAAO,QAAQ;;MAE1C,kBACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,eAAO,aAAa,EAAE,EAAE,OAAO,QAAQ;;MAE3C,WACE,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5B,YAAI,UAAU;AACZ,oBAAU,KAAK;;AAGjB,eAAO;;MAEX,kBACE,cAAY,CAAC,EAAE,IAAI,SAAQ,MAAM;AAC/B,YAAI,UAAU;AACZ,gBAAM,YAAY,cAAc,OAAO,GAAG,KAAK,SAAS,YAAY,SAAS,QAAQ;AAGrF,aAAG,aAAa,SAAS;;AAG3B,eAAO;;;;EAKf,uBAAoB;AAClB,WAAO;MACL,KAAK,MAAK;AACR,YAAI,KAAK,OAAO,SAAS,aAAY,GAAI;AACvC,iBAAO;;AAGT,YAAI,CAAC,KAAK,OAAO,IAAG,EAAG,YAAW,GAAI;AACpC,iBAAO;;AAGT,eAAO,KAAK,OAAO,MAAK,EAAG,YAAW,EAAG,aAAY,EAAG,IAAG;;MAE7D,aAAa,MAAM,KAAK,OAAO,SAAS,iBAAgB;MACxD,WAAW;MACX,iBAAiB;MACjB,QAAQ;MACR,cAAc;;;EAIlB,wBAAqB;AACnB,UAAM,cAAc,KAAK,QAAQ,aAAa,KAAK,OAAO;AAE1D,WAAO;MACL,GAAI,cACA;QACA,eAAe;UACb,aAAa,KAAK,QAAQ;UAC1B,cAAc,KAAK,QAAQ;UAC3B,qBAAqB,KAAK,QAAQ;UAClC,MAAM,KAAK,QAAQ;UACnB,qBAAqB,KAAK,QAAQ;SACnC;MACF,IACC,CAAA;MACJ,aAAa;QACX,yBAAyB,KAAK,QAAQ;OACvC;;;EAIL,iBAAiB,WAAS;AACxB,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,SAAS,UAAU;MACnB,SAAS,UAAU;;AAGrB,WAAO;MACL,WAAW,aAAa,kBAAkB,WAAW,aAAa,OAAO,CAAC;;;AAG/E,CAAA;", "names": ["TableView"]}