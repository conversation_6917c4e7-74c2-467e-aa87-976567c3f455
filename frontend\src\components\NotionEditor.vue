<template>
  <div class="notion-editor">
    <div class="editor-toolbar">
      <el-button-group>
        <el-button
          @click="editor.chain().focus().toggleBold().run()"
          :class="{ 'is-active': editor?.isActive('bold') }"
          size="small"
        >
          B
        </el-button>
        <el-button
          @click="editor.chain().focus().toggleItalic().run()"
          :class="{ 'is-active': editor?.isActive('italic') }"
          size="small"
        >
          I
        </el-button>
        <el-button
          @click="editor.chain().focus().toggleCode().run()"
          :class="{ 'is-active': editor?.isActive('code') }"
          size="small"
        >
          &lt;/&gt;
        </el-button>
      </el-button-group>
      
      <el-button-group>
        <el-button 
          @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
          :class="{ 'is-active': editor?.isActive('heading', { level: 1 }) }"
          size="small"
        >
          H1
        </el-button>
        <el-button 
          @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
          :class="{ 'is-active': editor?.isActive('heading', { level: 2 }) }"
          size="small"
        >
          H2
        </el-button>
        <el-button 
          @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
          :class="{ 'is-active': editor?.isActive('heading', { level: 3 }) }"
          size="small"
        >
          H3
        </el-button>
      </el-button-group>
      
      <el-button-group>
        <el-button
          @click="editor.chain().focus().toggleBulletList().run()"
          :class="{ 'is-active': editor?.isActive('bulletList') }"
          size="small"
        >
          •
        </el-button>
        <el-button
          @click="editor.chain().focus().toggleOrderedList().run()"
          :class="{ 'is-active': editor?.isActive('orderedList') }"
          size="small"
        >
          1.
        </el-button>
        <el-button
          @click="editor.chain().focus().toggleCodeBlock().run()"
          :class="{ 'is-active': editor?.isActive('codeBlock') }"
          size="small"
        >
          { }
        </el-button>
      </el-button-group>
      
      <el-button
        @click="addImage"
        size="small"
      >
        📷
      </el-button>
    </div>
    
    <div class="editor-content">
      <editor-content :editor="editor" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
// 移除图标导入，使用文本替代

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const editor = useEditor({
  content: props.modelValue,
  extensions: [
    StarterKit,
    Image.configure({
      inline: true,
      allowBase64: true,
    }),
    Table.configure({
      resizable: true,
    }),
    TableRow,
    TableHeader,
    TableCell,
  ],
  onUpdate: ({ editor }) => {
    emit('update:modelValue', editor.getHTML())
  },
  editorProps: {
    attributes: {
      class: 'notion-editor-content',
    },
  },
})

const addImage = () => {
  const url = window.prompt('请输入图片URL:')
  if (url) {
    editor.value.chain().focus().setImage({ src: url }).run()
  }
}

watch(() => props.modelValue, (newValue) => {
  if (editor.value && newValue !== editor.value.getHTML()) {
    editor.value.commands.setContent(newValue)
  }
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})
</script>

<style lang="scss" scoped>
.notion-editor {
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius);
  overflow: hidden;
  background: var(--surface-color);
}

.editor-toolbar {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  background: #fafafa;
  
  .is-active {
    background: var(--primary-color);
    color: white;
  }
}

.editor-content {
  min-height: 300px;
  
  :deep(.notion-editor-content) {
    padding: 20px;
    outline: none;
    
    h1, h2, h3 {
      margin: 20px 0 10px 0;
      color: var(--text-color);
    }
    
    h1 {
      font-size: 28px;
      font-weight: bold;
    }
    
    h2 {
      font-size: 24px;
      font-weight: 600;
    }
    
    h3 {
      font-size: 20px;
      font-weight: 600;
    }
    
    p {
      margin: 10px 0;
      line-height: 1.6;
      color: var(--text-color);
    }
    
    ul, ol {
      padding-left: 20px;
      margin: 10px 0;
      
      li {
        margin: 5px 0;
        color: var(--text-color);
      }
    }
    
    pre {
      background: #f5f5f5;
      border-radius: 8px;
      padding: 15px;
      margin: 15px 0;
      overflow-x: auto;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
      }
    }
    
    code {
      background: #f0f0f0;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
    }
    
    img {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      margin: 10px 0;
    }
    
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 15px 0;
      
      th, td {
        border: 1px solid #ddd;
        padding: 8px 12px;
        text-align: left;
      }
      
      th {
        background: #f5f5f5;
        font-weight: 600;
      }
    }
    
    blockquote {
      border-left: 4px solid var(--primary-color);
      padding-left: 15px;
      margin: 15px 0;
      color: #666;
      font-style: italic;
    }
  }
}
</style>
