<template>
  <div class="home-container">
    <!-- 主题切换器 -->
    <ThemeSwitch />
    
    <!-- 顶部用户信息区域 -->
    <div class="user-header">
      <div class="user-avatar">
        <img :src="users[0].avatar" :alt="users[0].nickname" />
        <span>{{ users[0].nickname }}</span>
      </div>
      <div class="love-heart">
        <i class="heart-icon">💕</i>
      </div>
      <div class="user-avatar">
        <img :src="users[1].avatar" :alt="users[1].nickname" />
        <span>{{ users[1].nickname }}</span>
      </div>
    </div>

    <!-- 恋爱时刻表 -->
    <LoveTimer />

    <!-- 飞车传信 -->
    <div class="message-section">
      <el-button
        type="primary"
        size="large"
        @click="navigateTo('/messages')"
        class="message-btn"
      >
        💌 飞车传信
      </el-button>
    </div>

    <!-- 功能模块卡片 -->
    <div class="feature-cards">
      <div class="card feature-card" @click="navigateTo('/blog')">
        <div class="card-icon">📝</div>
        <div class="card-content">
          <h3>点点滴滴</h3>
          <p>☀️今朝有酒今朝醉</p>
        </div>
      </div>
      <div class="card feature-card" @click="navigateTo('/albums')">
        <div class="card-icon">📸</div>
        <div class="card-content">
          <h3>时光相册</h3>
          <p>📸记录美好瞬间</p>
        </div>
      </div>
      <div class="card feature-card" @click="navigateTo('/blessings')">
        <div class="card-icon">📋</div>
        <div class="card-content">
          <h3>祝福板</h3>
          <p>📋写下你们的祝福</p>
        </div>
      </div>
      <div class="card feature-card" @click="navigateTo('/tasks')">
        <div class="card-icon">✅</div>
        <div class="card-content">
          <h3>任务管理</h3>
          <p>📋管理日常任务</p>
        </div>
      </div>
    </div>

    <!-- 标签云和相册展示区域 -->
    <div class="content-showcase">
      <div class="tags-section">
        <TagCloud
          :tags="popularTags"
          title="热门标签"
          @tag-click="handleTagClick"
        />
      </div>
      
      <div class="recent-albums">
        <h3>最近相册</h3>
        <div class="album-grid">
          <div 
            v-for="album in recentAlbums" 
            :key="album.id"
            class="album-item"
            @click="navigateTo(`/albums/${album.id}`)"
          >
            <img :src="album.cover" :alt="album.name" />
            <div class="album-info">
              <h4>{{ album.name }}</h4>
              <span>{{ album.date }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer">
      <p>云想衣裳花想容，春风拂槛露华浓。</p>
      <p>愿这个小小的网站，记录下你们最美好的时光！💕</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'
import LoveTimer from '@/components/LoveTimer.vue'
import ThemeSwitch from '@/components/ThemeSwitch.vue'
import TagCloud from '@/components/TagCloud.vue'

const router = useRouter()
const userStore = useUserStore()
const themeStore = useThemeStore()

const users = ref(userStore.users)

// 模拟数据
const popularTags = ref([
  { id: 1, name: '九寨沟', count: 4 },
  { id: 2, name: '四姑娘山', count: 4 },
  { id: 3, name: '手办', count: 10 },
  { id: 4, name: '积木', count: 1 },
  { id: 5, name: '编织', count: 6 },
  { id: 6, name: '黄山', count: 1 }
])

const recentAlbums = ref([
  { id: 1, name: '沉木', cover: '/images/album1.jpg', date: '2023-07-17' },
  { id: 2, name: '波光粼粼', cover: '/images/album2.jpg', date: '2023-07-17' },
  { id: 3, name: '诺日朗瀑布', cover: '/images/album3.jpg', date: '2023-07-17' },
  { id: 4, name: '箭竹海瀑布', cover: '/images/album4.jpg', date: '2023-07-17' }
])

const navigateTo = (path) => {
  router.push(path)
}

const handleTagClick = (tag) => {
  navigateTo(`/albums?tag=${tag.name}`)
}

onMounted(() => {
  try {
    themeStore.initTheme()
    userStore.initUser()
  } catch (error) {
    console.error('初始化错误:', error)
  }
})
</script>

<style lang="scss" scoped>
.home-container {
  min-height: 100vh;
  padding: 20px 0;
}

.user-header {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  padding: 40px 20px;
  
  .user-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    
    img {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      border: 3px solid var(--primary-color);
      object-fit: cover;
    }
    
    span {
      font-size: 18px;
      font-weight: 500;
      color: var(--text-color);
    }
  }
  
  .love-heart {
    font-size: 40px;
  }
}

.message-section {
  text-align: center;
  margin: 30px 0;
  
  .message-btn {
    font-size: 16px;
    padding: 15px 30px;
    border-radius: 25px;
  }
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  max-width: 1000px;
  margin: 40px auto;
  padding: 0 20px;
  
  .feature-card {
    cursor: pointer;
    text-align: center;
    padding: 30px 20px;
    
    .card-icon {
      font-size: 48px;
      margin-bottom: 15px;
    }
    
    .card-content {
      h3 {
        font-size: 20px;
        margin-bottom: 8px;
        color: var(--text-color);
      }
      
      p {
        color: var(--primary-color);
        font-size: 14px;
      }
    }
  }
}

.content-showcase {
  max-width: 1000px;
  margin: 60px auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  
  h3 {
    margin-bottom: 20px;
    color: var(--text-color);
    font-size: 18px;
  }
  
  .tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    
    .tag-item {
      cursor: pointer;
      transition: var(--transition);
      
      &:hover {
        transform: scale(1.05);
      }
    }
  }
  
  .album-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    
    .album-item {
      cursor: pointer;
      border-radius: var(--border-radius);
      overflow: hidden;
      transition: var(--transition);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--box-shadow);
      }
      
      img {
        width: 100%;
        height: 120px;
        object-fit: cover;
      }
      
      .album-info {
        padding: 10px;
        background: var(--surface-color);
        
        h4 {
          font-size: 14px;
          margin-bottom: 4px;
          color: var(--text-color);
        }
        
        span {
          font-size: 12px;
          color: var(--primary-color);
        }
      }
    }
  }
}

.footer {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-color);
  opacity: 0.7;
  
  p {
    margin-bottom: 10px;
  }
}

@media (max-width: 768px) {
  .user-header {
    gap: 20px;
    
    .user-avatar img {
      width: 60px;
      height: 60px;
    }
    
    .love-heart {
      font-size: 30px;
    }
  }
  
  .feature-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    
    .feature-card {
      padding: 20px 15px;
      
      .card-icon {
        font-size: 36px;
      }
    }
  }
  
  .content-showcase {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}
</style>
