<template>
  <div class="theme-switch">
    <el-dropdown @command="handleThemeChange">
      <el-button type="primary" circle>
        🎨
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item 
            v-for="(theme, key) in themes" 
            :key="key"
            :command="key"
            :class="{ active: currentTheme === key }"
          >
            <div class="theme-item">
              <div 
                class="theme-color" 
                :style="{ backgroundColor: theme.primary }"
              ></div>
              <span>{{ theme.name }}</span>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()
const themes = computed(() => themeStore.themes)
const currentTheme = computed(() => themeStore.currentTheme)

const handleThemeChange = (themeKey) => {
  themeStore.setTheme(themeKey)
}
</script>

<style lang="scss" scoped>
.theme-switch {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.theme-item {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .theme-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  }
}

.active {
  background-color: var(--primary-color);
  color: white;
}
</style>
