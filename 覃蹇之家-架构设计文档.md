# 覃蹇之家 - 情侣专属网站架构设计文档

## 📋 项目概述

### 项目名称
覃蹇之家 - 情侣专属网站

### 项目描述
一个专为情侣打造的私人网站，支持博客记录、任务管理、照片分享、纪念日管理等功能，提供温馨的数字化生活记录平台。

### 核心特性
- 🏠 **私人专属**：仅限两个固定账户使用，访客只读模式
- 💕 **恋爱时刻表**：动态显示恋爱时长，温馨浪漫，参考poetize.cn/love设计
- 📝 **博客系统**：参考Notion块编辑器，支持富文本、代码、图片等多种内容类型
- ✅ **任务管理**：可拖拽卡片式任务，支持公开/私密设置
- 📸 **相册管理**：自定义标签分类，批量上传处理，标签云展示
- 🎨 **主题切换**：4套精美主题，随心情切换
- 💌 **飞车传信**：情侣间私密消息传递功能
- 🎯 **祝福板**：朋友祝福留言功能

## 👥 用户画像与使用场景

### 主要用户
- **主账户**：网站管理员，拥有全部权限
- **女朋友账户**：内容编辑者，拥有编辑权限
- **访客用户**：朋友/家人，只能查看公开内容

### 核心使用场景
1. **日常记录**：使用Notion风格编辑器写博客记录生活点滴
2. **任务管理**：管理个人和共同任务
3. **回忆分享**：上传照片，创建美好回忆，标签云分类展示
4. **纪念日管理**：记录重要日期，实时查看恋爱时长
5. **情侣互动**：通过飞车传信功能进行私密交流
6. **朋友祝福**：朋友可在祝福板留下美好祝愿

## 🏗️ 技术栈选型

### 前端技术栈
- **框架**：Vue 3 + Composition API
- **构建工具**：Vite
- **UI组件库**：Element Plus / Naive UI
- **状态管理**：Pinia
- **路由管理**：Vue Router 4
- **HTTP客户端**：Axios
- **样式处理**：SCSS/CSS3
- **图标库**：Element Plus Icons
- **富文本编辑器**：@tiptap/vue-3 (Notion风格块编辑器)
- **拖拽功能**：Vue.Draggable.Next
- **动画库**：@vueuse/motion

### 后端技术栈
- **框架**：FastAPI (Python 3.9+)
- **ORM**：SQLAlchemy 2.0
- **数据库**：MySQL 8.0
- **文件存储**：MinIO (本地部署)
- **图片处理**：Pillow
- **身份验证**：JWT
- **API文档**：FastAPI自动生成Swagger

### 开发工具
- **版本控制**：Git
- **代码格式化**：Prettier (前端) + Black (后端)
- **容器化**：Docker + Docker Compose
- **开发环境**：VS Code

## 🏛️ 系统架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[Vue 3 前端应用]
        B[移动端浏览器]
    end
    
    subgraph "网关层"
        C[Nginx 反向代理]
    end
    
    subgraph "应用层"
        D[FastAPI 后端服务]
        E[JWT 认证中间件]
        F[文件上传处理]
    end
    
    subgraph "数据层"
        G[MySQL 数据库]
        H[MinIO 对象存储]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    D --> F
    D --> G
    F --> H
```

### 模块架构图

```mermaid
graph LR
    subgraph "前端模块"
        A1[首页模块]
        A2[博客模块-Notion风格]
        A3[任务模块]
        A4[相册模块]
        A5[纪念日模块]
        A6[用户模块]
        A7[主题模块]
        A8[飞车传信模块]
        A9[祝福板模块]
    end

    subgraph "后端模块"
        B1[用户认证模块]
        B2[博客管理模块]
        B3[任务管理模块]
        B4[相册管理模块]
        B5[纪念日模块]
        B6[文件处理模块]
        B7[权限控制模块]
        B8[消息通信模块]
        B9[祝福留言模块]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    A4 --> B6
    A5 --> B5
    A6 --> B1
    A7 --> B1
    A8 --> B8
    A9 --> B9
```

## 📊 数据库设计

### 核心数据表结构

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    avatar_url VARCHAR(255),
    role ENUM('admin', 'editor') DEFAULT 'editor',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 博客文章表 (posts)
```sql
CREATE TABLE posts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    content JSON,  -- 存储Notion风格的块结构数据
    content_text TEXT,  -- 纯文本内容，用于搜索
    summary VARCHAR(500),
    author_id INT NOT NULL,
    category_id INT,
    is_public BOOLEAN DEFAULT TRUE,
    is_draft BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    publish_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

#### 3. 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    user_id INT NOT NULL,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    progress INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    due_date TIMESTAMP NULL,
    position_x INT DEFAULT 0,
    position_y INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 4. 相册表 (albums)
```sql
CREATE TABLE albums (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    cover_photo_id INT,
    user_id INT NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 5. 照片表 (photos)
```sql
CREATE TABLE photos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255),
    file_path VARCHAR(500) NOT NULL,
    thumbnail_path VARCHAR(500),
    file_size INT,
    mime_type VARCHAR(100),
    width INT,
    height INT,
    album_id INT,
    user_id INT NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    taken_at TIMESTAMP NULL,
    location VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (album_id) REFERENCES albums(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 6. 纪念日表 (anniversaries)
```sql
CREATE TABLE anniversaries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    date DATE NOT NULL,
    type ENUM('anniversary', 'birthday', 'custom') DEFAULT 'custom',
    is_yearly BOOLEAN DEFAULT TRUE,
    reminder_days INT DEFAULT 0,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 7. 飞车传信表 (messages)
```sql
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    content TEXT NOT NULL,
    message_type ENUM('text', 'image', 'file') DEFAULT 'text',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (receiver_id) REFERENCES users(id)
);
```

#### 8. 祝福板表 (blessings)
```sql
CREATE TABLE blessings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    author_name VARCHAR(50) NOT NULL,
    author_email VARCHAR(100),
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT FALSE,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP NULL
);
```

#### 9. 标签表 (tags)
```sql
CREATE TABLE tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    color VARCHAR(7) DEFAULT '#1890ff',
    usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 10. 照片标签关联表 (photo_tags)
```sql
CREATE TABLE photo_tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    photo_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (photo_id) REFERENCES photos(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    UNIQUE KEY unique_photo_tag (photo_id, tag_id)
);
```

## 🔌 API接口设计

### 认证相关接口
```python
# 用户登录
POST /api/auth/login
{
    "username": "string",
    "password": "string"
}

# 获取当前用户信息
GET /api/auth/me

# 刷新Token
POST /api/auth/refresh

# 用户登出
POST /api/auth/logout
```

### 博客相关接口
```python
# 获取文章列表
GET /api/posts?page=1&size=10&category_id=1&is_public=true

# 获取文章详情
GET /api/posts/{post_id}

# 创建文章
POST /api/posts
{
    "title": "string",
    "content": {  // Notion风格块结构
        "blocks": [
            {
                "id": "block_id",
                "type": "paragraph",
                "content": "文本内容",
                "properties": {}
            },
            {
                "id": "block_id_2",
                "type": "heading",
                "content": "标题内容",
                "properties": {"level": 1}
            }
        ]
    },
    "category_id": 1,
    "tags": ["tag1", "tag2"],
    "is_public": true,
    "is_draft": false
}

# 更新文章
PUT /api/posts/{post_id}

# 删除文章
DELETE /api/posts/{post_id}

# 实时协作编辑
WebSocket /api/posts/{post_id}/collaborate
```

### 任务相关接口
```python
# 获取任务列表
GET /api/tasks?user_id=1&status=pending

# 创建任务
POST /api/tasks
{
    "title": "string",
    "description": "string",
    "priority": "medium",
    "due_date": "2024-01-01T00:00:00",
    "is_public": false
}

# 更新任务位置
PUT /api/tasks/{task_id}/position
{
    "position_x": 100,
    "position_y": 200
}

# 删除任务
DELETE /api/tasks/{task_id}
```

### 飞车传信相关接口
```python
# 发送消息
POST /api/messages
{
    "receiver_id": 1,
    "content": "消息内容",
    "message_type": "text"
}

# 获取消息列表
GET /api/messages?page=1&size=20

# 标记消息已读
PUT /api/messages/{message_id}/read

# 获取未读消息数量
GET /api/messages/unread-count
```

### 祝福板相关接口
```python
# 提交祝福
POST /api/blessings
{
    "author_name": "string",
    "author_email": "string",
    "content": "祝福内容"
}

# 获取祝福列表
GET /api/blessings?page=1&size=10&approved=true

# 审核祝福（管理员）
PUT /api/blessings/{blessing_id}/approve
```

### 标签相关接口
```python
# 获取标签云
GET /api/tags/cloud

# 创建标签
POST /api/tags
{
    "name": "标签名",
    "color": "#1890ff"
}

# 获取热门标签
GET /api/tags/popular?limit=20
```

## 🎨 前端页面设计

### 页面结构
```
src/
├── components/          # 公共组件
│   ├── Layout/         # 布局组件
│   ├── LoveTimer/      # 恋爱时刻表组件（参考poetize.cn设计）
│   ├── TaskCard/       # 任务卡片组件
│   ├── ThemeSwitch/    # 主题切换组件
│   ├── NotionEditor/   # Notion风格编辑器组件
│   ├── TagCloud/       # 标签云组件
│   └── MessageBox/     # 飞车传信组件
├── views/              # 页面组件
│   ├── Home/           # 首页（参考poetize.cn/love布局）
│   ├── Blog/           # 博客（Notion风格编辑器）
│   ├── Tasks/          # 任务管理
│   ├── Albums/         # 相册
│   ├── Anniversaries/  # 纪念日
│   ├── Messages/       # 飞车传信
│   ├── Blessings/      # 祝福板
│   └── Settings/       # 设置
├── stores/             # 状态管理
├── utils/              # 工具函数
└── styles/             # 样式文件
```

### 首页设计（参考poetize.cn/love）
```vue
<!-- Home.vue 首页布局 -->
<template>
  <div class="home-container">
    <!-- 顶部用户信息区域 -->
    <div class="user-header">
      <div class="user-avatar">
        <img :src="user1.avatar" :alt="user1.nickname" />
        <span>{{ user1.nickname }}</span>
      </div>
      <div class="love-heart">
        <i class="heart-icon">💕</i>
      </div>
      <div class="user-avatar">
        <img :src="user2.avatar" :alt="user2.nickname" />
        <span>{{ user2.nickname }}</span>
      </div>
    </div>

    <!-- 恋爱时刻表 -->
    <div class="love-timer">
      <h2>这是我们一起走过的</h2>
      <div class="timer-display">
        第{{ years }}年{{ months }}月{{ days }}日{{ hours }}时{{ minutes }}分{{ seconds }}秒
      </div>
      <div class="countdown">
        春节倒计时: {{ springFestivalCountdown }}
      </div>
    </div>

    <!-- 飞车传信 -->
    <div class="message-section">
      <button @click="openMessageBox" class="message-btn">
        飞车传信
      </button>
    </div>

    <!-- 功能模块卡片 -->
    <div class="feature-cards">
      <div class="card" @click="navigateTo('/blog')">
        <img src="/icons/blog.png" alt="博客" />
        <div class="card-content">
          <h3>点点滴滴</h3>
          <p>☀️今朝有酒今朝醉</p>
        </div>
      </div>
      <div class="card" @click="navigateTo('/albums')">
        <img src="/icons/album.png" alt="相册" />
        <div class="card-content">
          <h3>时光相册</h3>
          <p>📸记录美好瞬间</p>
        </div>
      </div>
      <div class="card" @click="navigateTo('/blessings')">
        <img src="/icons/blessing.png" alt="祝福板" />
        <div class="card-content">
          <h3>祝福板</h3>
          <p>📋写下你们的祝福</p>
        </div>
      </div>
    </div>

    <!-- 标签云和相册展示区域 -->
    <div class="content-showcase">
      <TagCloud :tags="popularTags" />
      <AlbumPreview :albums="recentAlbums" />
    </div>
  </div>
</template>
```

### 主题配色方案
```scss
// 浪漫粉色主题
$theme-romantic: (
  primary: #ff6b9d,
  secondary: #ffc3d8,
  background: #fef7f7,
  surface: #ffffff
);

// 清新绿色主题
$theme-fresh: (
  primary: #4ecdc4,
  secondary: #a8e6cf,
  background: #f7fffe,
  surface: #ffffff
);

// 优雅蓝色主题
$theme-elegant: (
  primary: #4a90e2,
  secondary: #a8c8ec,
  background: #f7f9fc,
  surface: #ffffff
);

// 暖色橙色主题
$theme-warm: (
  primary: #ff8c42,
  secondary: #ffc49b,
  background: #fff8f3,
  surface: #ffffff
);
```

## 🚀 部署方案

### Docker容器化部署

#### docker-compose.yml
```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

  # 后端服务
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://user:password@mysql:3306/love_website
      - MINIO_ENDPOINT=minio:9000
    depends_on:
      - mysql
      - minio

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=love_website
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"

volumes:
  mysql_data:
  minio_data:
```

### 环境配置

#### 后端环境变量 (.env)
```env
# 数据库配置
DATABASE_URL=mysql://user:password@localhost:3306/love_website

# JWT配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# MinIO配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=love-website

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp
```

## 📋 开发流程与步骤

### 第一阶段：项目初始化 (1-2天)

#### 1.1 环境搭建
```bash
# 创建项目目录
mkdir love-website && cd love-website

# 初始化前端项目
npm create vue@latest frontend
cd frontend && npm install

# 初始化后端项目
mkdir backend && cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install fastapi uvicorn sqlalchemy pymysql python-multipart pillow minio python-jose[cryptography] passlib[bcrypt]
```

#### 1.2 数据库初始化
```bash
# 启动MySQL和MinIO
docker-compose up mysql minio -d

# 创建数据库表
mysql -u user -p love_website < init.sql
```

#### 1.3 基础项目结构搭建
- 创建前端路由和基础页面
- 搭建后端FastAPI应用结构
- 配置数据库连接和ORM模型

### 第二阶段：用户认证系统 (2-3天)

#### 2.1 后端认证实现
```python
# backend/app/auth.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer
from jose import JWTError, jwt
from passlib.context import CryptContext

# 实现JWT认证、密码加密、权限验证
```

#### 2.2 前端认证集成
```javascript
// frontend/src/stores/auth.js
import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  // 实现登录状态管理、Token存储、路由守卫
})
```

#### 2.3 预置用户数据
```sql
-- 插入预设用户账户
INSERT INTO users (username, email, password_hash, nickname, role) VALUES
('admin', '<EMAIL>', '$2b$12$...', '主账户', 'admin'),
('girlfriend', '<EMAIL>', '$2b$12$...', '女朋友', 'editor');
```

### 第三阶段：核心功能模块开发 (10-12天)

#### 3.1 博客模块 (3-4天)
**后端开发**：
- 实现文章CRUD接口，支持JSON块结构存储
- 分类和标签管理
- Notion风格块内容处理和解析
- 权限控制逻辑
- WebSocket实时协作编辑

**前端开发**：
- 文章列表和详情页面
- Notion风格块编辑器集成（使用@tiptap/vue-3）
- 支持多种块类型：文本、标题、代码、图片、表格、列表等
- 拖拽排序功能
- 分类标签选择组件
- 草稿自动保存功能
- 实时协作编辑界面

#### 3.2 任务管理模块 (2-3天)
**后端开发**：
- 任务CRUD接口
- 位置信息保存
- 进度跟踪逻辑
- 公开/私密权限

**前端开发**：
- 参考ui.html实现拖拽卡片
- 任务状态管理
- 排列方式切换
- 进度条和优先级显示

#### 3.3 相册模块 (2-3天)
**后端开发**：
- 文件上传处理
- MinIO集成
- 图片压缩和缩略图生成
- 标签系统和标签云统计
- 照片标签关联管理

**前端开发**：
- 相册网格布局
- 批量上传组件
- 图片预览和幻灯片
- 标签筛选功能
- 标签云展示组件（参考poetize.cn设计）
- 照片标签管理界面

#### 3.4 纪念日模块 (1-2天)
**后端开发**：
- 纪念日CRUD接口
- 时间计算逻辑（参考poetize.cn的计时器）
- 提醒功能

**前端开发**：
- 恋爱时刻表组件（参考poetize.cn设计风格）
- 纪念日管理页面
- 动态时间显示和倒计时功能

#### 3.5 飞车传信模块 (1-2天)
**后端开发**：
- 消息CRUD接口
- WebSocket实时通信
- 消息状态管理

**前端开发**：
- 消息发送界面
- 实时消息接收
- 消息历史查看

#### 3.6 祝福板模块 (1天)
**后端开发**：
- 祝福留言CRUD接口
- 内容审核机制
- IP地址记录

**前端开发**：
- 祝福提交表单
- 祝福展示列表
- 管理员审核界面

### 第四阶段：UI优化和主题系统 (3-4天)

#### 4.1 主题系统实现
```javascript
// frontend/src/composables/useTheme.js
export function useTheme() {
  const themes = {
    romantic: { /* 浪漫粉色配色 */ },
    fresh: { /* 清新绿色配色 */ },
    elegant: { /* 优雅蓝色配色 */ },
    warm: { /* 暖色橙色配色 */ }
  }

  // 主题切换逻辑
}
```

#### 4.2 响应式设计优化
- 移动端适配
- 触摸操作优化
- 性能优化

#### 4.3 用户体验提升
- 加载动画
- 错误提示
- 操作反馈

### 第五阶段：测试和部署 (2-3天)

#### 5.1 功能测试
- 用户认证流程测试
- 各模块功能测试
- 权限控制测试
- 文件上传测试

#### 5.2 性能优化
- 图片懒加载
- 接口响应优化
- 前端打包优化

#### 5.3 生产部署
```bash
# 构建前端
cd frontend && npm run build

# 构建Docker镜像
docker-compose build

# 启动生产环境
docker-compose up -d
```

### 第六阶段：文档和维护 (1天)

#### 6.1 用户手册编写
- 功能使用说明
- 常见问题解答

#### 6.2 技术文档完善
- API文档更新
- 部署文档完善
- 维护指南编写

## 🔒 安全考虑

### 基础安全措施
1. **HTTPS加密**：使用SSL证书
2. **SQL注入防护**：使用ORM参数化查询
3. **XSS防护**：前端输入验证和转义
4. **CSRF保护**：Token验证
5. **文件上传安全**：类型和大小限制
6. **密码安全**：bcrypt加密存储

### 权限控制
```python
# 权限装饰器示例
def require_permission(permission: str):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 权限验证逻辑
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

## 📈 性能优化

### 前端优化
- 组件懒加载
- 图片懒加载
- 虚拟滚动
- 缓存策略

### 后端优化
- 数据库索引优化
- 查询优化
- 文件缓存
- 响应压缩

## 🔧 维护和扩展

### 日志系统
```python
# 配置日志记录
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
```

### 备份策略
- 数据库定期备份
- 文件存储备份
- 配置文件备份

### 监控告警
- 系统资源监控
- 应用性能监控
- 错误日志监控

## 📝 总结

本架构设计文档为"覃蹇之家"情侣专属网站提供了完整的技术方案和开发指南。采用Vue 3 + FastAPI的现代化技术栈，结合MySQL数据库和MinIO对象存储，实现了一个功能完整、安全可靠的私人网站系统。

整个开发周期预计18-22天，分为6个阶段逐步实施。通过模块化设计和容器化部署，确保系统的可维护性和可扩展性。

项目特别注重用户体验，提供了Notion风格博客编辑器、拖拽式任务管理、多主题切换、恋爱时刻表、飞车传信、祝福板等特色功能，参考poetize.cn/love的设计风格，为情侣用户打造了一个温馨浪漫的数字化生活记录平台。

## 🎯 快速开始

### 1. 克隆项目模板
```bash
git clone <repository-url>
cd love-website
```

### 2. 启动开发环境
```bash
# 启动数据库和存储服务
docker-compose up mysql minio -d

# 启动后端服务
cd backend
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
uvicorn main:app --reload

# 启动前端服务
cd ../frontend
npm install
npm run dev
```

### 3. 访问应用
- 前端应用：http://localhost:5173
- 后端API：http://localhost:8000
- API文档：http://localhost:8000/docs
- MinIO控制台：http://localhost:9001

祝你们的专属网站开发顺利！💕
